from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_socketio import Socket<PERSON>
from config.aps_config import config as aps_config, FlaskConfig
import os
from flask_cors import CORS
from flask_migrate import Migrate
from dotenv import load_dotenv

# 在应用启动的早期加载.env文件（新配置系统会自动处理）
# load_dotenv()  # 注释掉，由aps_config自动处理

# 🔧 应用MySQL猴子补丁修复硬编码localhost问题
try:
    from app.utils.mysql_monkey_patch import apply_mysql_monkey_patch, get_current_mysql_config
    apply_mysql_monkey_patch()
    config_info = get_current_mysql_config()
    print(f"[OK] MySQL monkey patch enabled: {config_info['host']}:{config_info['port']}")
except Exception as e:
    print(f"[WARN] MySQL monkey patch failed: {e}")

db = SQLAlchemy()
migrate = Migrate()
login_manager = LoginManager()
login_manager.login_view = 'auth.login'
login_manager.login_message = None

# 创建全局SocketIO实例
socketio = SocketIO()

# 创建全局调度器变量
scheduler = None

# 全局Flask应用实例变量
app = None

def create_app(config_class=None):
    global app
    app = Flask(__name__)
    
    # 启用CORS支持
    CORS(app, supports_credentials=True)

    # 启用缓存优化和压缩
    try:
        from app.utils.cache_optimizer import CacheOptimizer, init_compression
        cache_optimizer = CacheOptimizer(app)
        init_compression(app)
        app.logger.info('✅ 缓存优化和压缩已启用')
    except Exception as e:
        app.logger.warning(f'⚠️ 缓存优化启用失败: {e}')
    
    # 确保实例文件夹存在
    try:
        os.makedirs(app.instance_path, exist_ok=True)
    except OSError:
        pass
    
    # 使用新的统一配置系统
    if config_class is None:
        flask_config = FlaskConfig()
    else:
        # 向后兼容：如果传入的是旧的配置类，则使用新配置系统
        # 否则使用传入的配置对象
        flask_config = FlaskConfig() if hasattr(config_class, 'SQLALCHEMY_DATABASE_URI') else config_class
    
    # 从环境变量获取数据库URI（如果存在则覆盖）
    if 'DATABASE_URL' in os.environ:
        app.config['SQLALCHEMY_DATABASE_URI'] = os.environ['DATABASE_URL']
        app.logger.info("使用环境变量DATABASE_URL覆盖数据库配置")
    else:
        # 加载统一配置
        app.config.from_object(flask_config)
        app.logger.info(f"使用新统一配置系统：{aps_config.DB_HOST}:{aps_config.DB_PORT}/{aps_config.DB_NAME}")
    
    # 初始化扩展
    db.init_app(app)
    login_manager.init_app(app)
    migrate.init_app(app, db)
    
    # 🔧 修复：初始化性能监控器
    try:
        from app.utils.performance_monitor import PerformanceMonitor
        performance_monitor = PerformanceMonitor(app)
        # 简化：移除性能监控器日志
    except ImportError as e:
        app.logger.warning(f'⚠️ 性能监控器初始化失败: {e}')
    
    # 初始化SocketIO - 兼容PyInstaller打包环境
    import sys
    if getattr(sys, 'frozen', False):
        # PyInstaller环境，禁用SocketIO避免兼容性问题
        app.config['SOCKETIO_ENABLED'] = False
        app.logger.info('🔧 PyInstaller环境：SocketIO已禁用')
    else:
        # 开发环境，正常启用SocketIO
        socketio.init_app(app, cors_allowed_origins="*", logger=True, engineio_logger=True)
        app.config['SOCKETIO_ENABLED'] = True
    
    # 数据库表检查 - 安全模式（不自动创建表）
    with app.app_context():
        try:
            from sqlalchemy import inspect
            
            # 检查主数据库连接和表状态
            inspector = inspect(db.engine)
            existing_tables = inspector.get_table_names()
            
            if not existing_tables:
                app.logger.warning('[WARNING] 数据库中没有发现任何表')
                app.logger.info('[INFO] 请运行以下命令初始化数据库:')
                app.logger.info('[INFO]   python run.py init-db')
                app.logger.info('[INFO] 或者')
                app.logger.info('[INFO]   python tools/database/init_db.py')
            else:
                app.logger.info(f'[OK] 数据库连接正常，发现 {len(existing_tables)} 个表')
                
                # 检查关键业务表是否存在
                critical_tables = ['users', 'user_permissions', 'ct', 'wip_lot', 'et_wait_lot']
                missing_critical = [table for table in critical_tables if table not in existing_tables]
                
                if missing_critical:
                    app.logger.warning(f'[WARNING] 缺少关键业务表: {", ".join(missing_critical)}')
                    app.logger.info('[INFO] 建议运行数据库初始化: python run.py init-db')
                else:
                    app.logger.info('[OK] 所有关键业务表都已存在')
            
            # 检查绑定数据库（如果有）
            binds = app.config.get('SQLALCHEMY_BINDS', {})
            for bind_key in binds.keys():
                try:
                    # 修复：使用新的SQLAlchemy API
                    bind_engine = db.engines.get(bind_key) if hasattr(db, 'engines') else None
                    if bind_engine:
                        bind_inspector = inspect(bind_engine)
                        bind_tables = bind_inspector.get_table_names()
                        
                        if not bind_tables:
                            app.logger.warning(f'[WARNING] 绑定数据库 {bind_key} 中没有表')
                        else:
                            app.logger.info(f'[OK] 绑定数据库 {bind_key} 有 {len(bind_tables)} 个表')
                    else:
                        app.logger.warning(f'[WARNING] 无法获取绑定数据库 {bind_key} 的引擎')
                        
                except Exception as e:
                    app.logger.error(f'[ERROR] 无法检查绑定数据库 {bind_key}: {e}')
                    
        except Exception as e:
            app.logger.error(f'[ERROR] 数据库检查失败: {e}')
            app.logger.info('[INFO] 请确保MySQL服务运行正常并运行数据库初始化')
    
    # 注册用户加载函数
    from app.models import User
    
    @login_manager.user_loader
    def load_user(username):
        return User.query.get(username)
    
    # 添加版本号到上下文 - 使用统一版本号管理
    @app.context_processor
    def inject_version():
        try:
            # 🏷️ 从统一版本号管理中心获取
            from app.services import APP_VERSION
            return {'app_version': APP_VERSION}
        except ImportError:
            # 降级方案：从配置读取
            return {'app_version': app.config.get('APP_VERSION', '2.3.4')}
    
    # 注册上下文处理器
    try:
        from app.context_processors import register_context_processors
        register_context_processors(app)
        # 简化：移除上下文处理器日志
    except Exception as e:
        app.logger.warning(f'⚠️ 上下文处理器注册失败: {e}')
    
    # 注册核心蓝图
    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    from app.main import bp as main_bp
    app.register_blueprint(main_bp)
    
    # 🔥 优先注册手动排产API蓝图（避免路由冲突）
    try:
        from app.api_v2.production.manual_scheduling_api import manual_scheduling_api
        app.register_blueprint(manual_scheduling_api)
        # 简化：移除蓝图注册日志
    except ImportError as e:
        app.logger.error(f'❌ 手动排产API蓝图注册失败: {e}')
    
    from app.api import bp as api_bp
    app.register_blueprint(api_bp, url_prefix='/api')
    
    # 注册邮件附件处理蓝图
    from app.api.email_attachment import email_attachment_bp
    app.register_blueprint(email_attachment_bp)
    
    # 注册订单处理API蓝图 (已禁用，使用V2版本)
    # from app.api.order_processing_api import order_processing_bp
    # app.register_blueprint(order_processing_bp)
    
    # 注册生产管理蓝图
    from app.routes.production_views import production_views_bp
    app.register_blueprint(production_views_bp)
            # 简化：移除蓝图注册日志
    
    # 注册订单管理蓝图
    from app.routes.orders_routes import bp as orders_bp
    app.register_blueprint(orders_bp)
    
    # 注册系统管理蓝图
    from app.routes.system import system_bp
    app.register_blueprint(system_bp)
    
    # 注册API v2蓝图
    try:
        # 注册订单API v2蓝图
        from app.api_v2.orders import orders_bp as orders_v2_bp
        app.register_blueprint(orders_v2_bp)
        
        # 注册其他API v2蓝图
        from app.api_v2.orders.summary_preview_api import bp as summary_preview_api_bp
        app.register_blueprint(summary_preview_api_bp, url_prefix='/api/v2/orders')
        
        from app.api_v2.orders.high_concurrency_api import high_concurrency_bp
        app.register_blueprint(high_concurrency_bp)
        
        from app.api_v2.production.routes import production_bp as production_api_v2_bp
        app.register_blueprint(production_api_v2_bp)
        
        # 单独注册done_lots_api蓝图
        from app.api_v2.production.done_lots_api import done_lots_bp
        app.register_blueprint(done_lots_bp)
        
        # 注册手动调整API蓝图 - Phase 2.3
        from app.api_v2.production.manual_adjustment_api import manual_adjustment_api
        app.register_blueprint(manual_adjustment_api)
        
        # 注册最终排产调整结果API蓝图
        from app.api_v2.production.final_scheduling_api import final_scheduling_api
        app.register_blueprint(final_scheduling_api)
        
        from app.api_v2.resources.routes import resources_bp as resources_api_v2_bp
        app.register_blueprint(resources_api_v2_bp)
        
        from app.api_v2.system import system_bp as system_api_v2_bp
        app.register_blueprint(system_api_v2_bp)
        
        # 注册多级缓存API蓝图 - Task 2.2
        from app.api_v2.system.multilevel_cache_api import multilevel_cache_bp
        app.register_blueprint(multilevel_cache_bp, url_prefix='/api/v2/system')
        
        # 注册并行计算API蓝图 - Task 2.3
        try:
            from app.api_v2.system.parallel_computing_api import parallel_computing_bp
            app.register_blueprint(parallel_computing_bp, url_prefix='/api/v2/system/parallel-computing')
        except Exception as e:
            app.logger.warning(f'⚠️ 并行计算API注册失败: {e}')
        
        # 注册Excel自动保存API蓝图
        try:
            from app.api_v2.system.excel_auto_save_api import excel_auto_save_bp
            app.register_blueprint(excel_auto_save_bp)
        except Exception as e:
            app.logger.warning(f'⚠️ Excel自动保存API注册失败: {e}')
        
        # 注册认证API v2蓝图
        from app.api_v2.auth import auth_bp as auth_api_v2_bp
        app.register_blueprint(auth_api_v2_bp)
        
        
        # 注册WIP批次管理API蓝图
        from app.api_v2.wip_lot_api import wip_lot_api_bp
        app.register_blueprint(wip_lot_api_bp)
        
        app.logger.info('✅ API服务已就绪')
        
    except ImportError as e:
        app.logger.error(f'❌ API v2蓝图注册失败: {e}')
        import traceback
        app.logger.error(traceback.format_exc())
    
    # 注册API v3蓝图 - 动态字段管理器 (迁移测试版本)
    try:
        from app.api.routes_v3 import api_v3 as api_v3_bp
        app.register_blueprint(api_v3_bp)
        # 简化：移除API v3注册日志
    except ImportError as e:
        app.logger.warning(f'⚠️ API v3蓝图注册失败: {e}')
        # 简化：移除开发阶段说明
    
    # 配置精简日志系统
    try:
        from app.utils.simple_logging import setup_logging
        setup_logging(app)
        # app.logger.info('✅ 统一日志系统已配置')  # 简化：移除技术性日志
    except Exception as e:
        import logging
        app.logger.setLevel(logging.INFO)
        app.logger.warning(f'⚠️ 日志系统配置失败: {e}')
        # app.logger.info('应用启动')  # 简化：移除冗余日志
    
    # 初始化和启动统一调度器服务
    with app.app_context():
        try:
            from app.services.scheduler_service import scheduler_service
            from app.models import SchedulerJob, SchedulerJobLog, SchedulerConfig
            
            # 确保调度器模型表已导入（用于自动创建表）
            scheduler_service.init_app(app)
            
            # 启动APScheduler
            if scheduler_service.start():
                app.logger.info('✅ 调度服务已启动')
                
                # 为了向后兼容，设置全局scheduler变量指向APScheduler服务
                import sys
                current_module = sys.modules[__name__]
                current_module.scheduler = scheduler_service
                
            else:
                app.logger.warning('⚠️ 调度器启动失败或被禁用')
                
        except Exception as e:
            app.logger.error(f'❌ 调度器启动失败: {e}')
            import traceback
            app.logger.error(traceback.format_exc())
    
    # 初始化后端定时任务服务 (替代前端定时任务)
    try:
        from app.services.background_scheduler_service import background_scheduler
        background_scheduler.init_app(app)
        
        # 延迟启动后端定时任务服务
        import threading
        def delayed_start():
            import time
            time.sleep(2)  # 等待应用完全初始化
            with app.app_context():
                background_scheduler.start()
                app.logger.info('✅ 排产任务服务已启动')
        
        threading.Thread(target=delayed_start, daemon=True).start()
        
    except Exception as e:
        app.logger.error(f'❌ 排产任务服务初始化失败: {e}')
        import traceback
        app.logger.error(traceback.format_exc())
    
    # 初始化邮箱定时任务服务
    try:
        from app.services.email_scheduler_service import init_email_scheduler_service
        
        # 延迟启动邮箱定时任务服务
        import threading
        def delayed_email_scheduler_start():
            import time
            time.sleep(3)  # 等待应用完全初始化
            with app.app_context():
                init_email_scheduler_service()
                app.logger.info('✅ 邮件服务已启动')
        
        threading.Thread(target=delayed_email_scheduler_start, daemon=True).start()
        
    except Exception as e:
        app.logger.error(f'❌ 邮件服务初始化失败: {e}')
        import traceback
        app.logger.error(traceback.format_exc())

    return app, socketio

# 导入模型
from app import models

# 全局调度器变量（在应用创建时设置）
scheduler = None 