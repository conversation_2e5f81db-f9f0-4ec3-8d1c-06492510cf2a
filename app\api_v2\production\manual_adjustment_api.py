from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
from app import db
from sqlalchemy import text
from datetime import datetime
import logging
import json

manual_adjustment_api = Blueprint('manual_adjustment_api', __name__)
logger = logging.getLogger(__name__)

@manual_adjustment_api.route('/api/v2/production/manual-adjustment/batch-update', methods=['POST'])
@login_required
def batch_update_priorities():
    """批量更新批次优先级和分选机分配"""
    try:
        # 检查用户会话状态
        if not current_user.is_authenticated:
            logger.warning("⚠️ 用户会话已过期，需要重新登录")
            return jsonify({
                'success': False,
                'message': '用户会话已过期，请刷新页面重新登录',
                'error_type': 'session_expired',
                'redirect_to_login': True
            }), 401
        
        data = request.get_json()
        if not data:
            logger.error("❌ 请求体为空或格式错误")
            return jsonify({
                'success': False,
                'message': '请求体为空或格式错误，请检查Content-Type是否为application/json',
                'error_type': 'invalid_request_body'
            }), 400
        
        updates = data.get('updates', [])
        operation_type = data.get('operation_type', 'drag_adjustment')
        session_id = data.get('session_id', None)
        
        logger.info(f"📥 收到批量更新请求: {len(updates)} 条记录, 操作类型: {operation_type}")
        logger.debug(f"📊 请求数据详情: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        if not updates:
            return jsonify({
                'success': False,
                'message': '没有提供更新数据',
                'error_type': 'no_updates_provided'
            }), 400
        
        # 验证updates是否为列表
        if not isinstance(updates, list):
            return jsonify({
                'success': False,
                'message': 'updates字段必须是数组格式',
                'error_type': 'invalid_updates_format'
            }), 400
        
        logger.info(f"🔄 开始批量更新 {len(updates)} 条记录，操作类型: {operation_type}")
        
        # 首先检查批量大小限制，防止一次处理过多记录
        max_batch_size = 200
        if len(updates) > max_batch_size:
            logger.warning(f"⚠️ 批量更新记录数量超限: {len(updates)} > {max_batch_size}")
            return jsonify({
                'success': False,
                'message': f'批量更新记录数量超限，最多支持 {max_batch_size} 条记录，当前: {len(updates)} 条',
                'max_batch_size': max_batch_size,
                'current_size': len(updates),
                'error_type': 'batch_size_limit'
            }), 400
        
        # 然后进行数据验证
        validation_result = validate_batch_updates(updates)
        if not validation_result['valid']:
            logger.error(f"❌ 数据验证失败: {validation_result['message']}")
            logger.debug(f"📋 验证错误详情: {validation_result['errors']}")
            return jsonify({
                'success': False,
                'message': f'数据验证失败: {validation_result["message"]}',
                'errors': validation_result['errors'],
                'error_type': 'data_validation',
                'debug_info': {
                    'total_updates': len(updates),
                    'validation_errors': validation_result['errors'],
                    'sample_update': updates[0] if updates else None
                }
            }), 400
        
        # 执行批量更新
        updated_count = 0
        failed_updates = []
        
        for update in updates:
            try:
                lot_id = update['lot_id']
                new_priority = update.get('priority')
                new_handler_id = update.get('handler_id')
                old_handler_id = update.get('old_handler_id')
                
                # 查找对应的批次记录（使用LOT_ID查找）
                check_query = text("SELECT id FROM lotprioritydone WHERE LOT_ID = :lot_id")
                result = db.session.execute(check_query, {'lot_id': lot_id})
                record = result.fetchone()
                
                if record:
                    # 构建更新SQL
                    update_fields = []
                    update_params = {'lot_id': lot_id}
                    
                    # 更新优先级
                    if new_priority is not None:
                        update_fields.append("PRIORITY = :priority")
                        update_params['priority'] = new_priority
                    
                    # 更新分选机分配
                    if new_handler_id:
                        update_fields.append("HANDLER_ID = :handler_id")
                        update_params['handler_id'] = new_handler_id
                    
                    # 如果是失败批次恢复，更新状态
                    if old_handler_id == 'UNASSIGNED' and new_handler_id and new_handler_id != 'UNASSIGNED':
                        update_fields.append("WIP_STATE = 'SCHEDULED'")
                        update_fields.append("PROC_STATE = 'READY'")
                    
                    if update_fields:
                        # 执行更新
                        update_sql = f"""
                            UPDATE lotprioritydone 
                            SET {', '.join(update_fields)}
                            WHERE LOT_ID = :lot_id
                        """
                        db.session.execute(text(update_sql), update_params)
                        updated_count += 1
                        logger.debug(f"✅ 更新批次 {lot_id}: priority={new_priority}, handler={new_handler_id}")
                    
                else:
                    failed_updates.append({
                        'lot_id': lot_id,
                        'reason': '批次记录不存在'
                    })
                    logger.warning(f"⚠️ 批次 {lot_id} 不存在")
                    
            except Exception as e:
                failed_updates.append({
                    'lot_id': update.get('lot_id', 'unknown'),
                    'reason': str(e)
                })
                logger.error(f"❌ 更新批次失败: {e}")
        
        # 提交事务
        db.session.commit()
        
        # 记录操作历史
        record_adjustment_history(updates, current_user.username, operation_type, session_id)
        
        response_data = {
            'success': True,
            'message': f'成功更新 {updated_count} 条记录',
            'updated_count': updated_count,
            'total_count': len(updates),
            'success_count': updated_count,
            'failed_count': len(failed_updates)
        }
        
        if failed_updates:
            response_data['failed_updates'] = failed_updates
            if updated_count > 0:
                response_data['message'] = f'部分更新成功: 成功 {updated_count} 条，跳过 {len(failed_updates)} 条失败批次'
            else:
                response_data['message'] = f'无批次更新: {len(failed_updates)} 条失败批次已跳过'
        
        logger.info(f"✅ 批量更新完成: {updated_count}/{len(updates)} 成功")
        
        return jsonify(response_data)
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"❌ 批量更新失败: {e}")
        return jsonify({
            'success': False,
            'message': f'更新失败: {str(e)}'
        }), 500

@manual_adjustment_api.route('/api/v2/production/manual-adjustment/validate', methods=['POST'])
@login_required
def validate_adjustments():
    """验证调整操作的合理性"""
    try:
        data = request.get_json()
        updates = data.get('updates', [])
        
        if not updates:
            return jsonify({
                'success': False,
                'message': '没有提供验证数据'
            }), 400
        
        # 执行验证
        validation_result = comprehensive_validation(updates)
        
        return jsonify({
            'success': True,
            'validation_result': validation_result
        })
        
    except Exception as e:
        logger.error(f"❌ 验证失败: {e}")
        return jsonify({
            'success': False,
            'message': f'验证失败: {str(e)}'
        }), 500

@manual_adjustment_api.route('/api/v2/production/manual-adjustment/handler-capacity', methods=['POST'])
@login_required
def check_handler_capacity():
    """检查分选机负载能力"""
    try:
        data = request.get_json()
        updates = data.get('updates', [])
        
        capacity_check = validate_handler_capacity(updates)
        
        return jsonify({
            'success': True,
            'capacity_check': capacity_check
        })
        
    except Exception as e:
        logger.error(f"❌ 负载检查失败: {e}")
        return jsonify({
            'success': False,
            'message': f'负载检查失败: {str(e)}'
        }), 500

def validate_batch_updates(updates):
    """验证批量更新数据的合理性"""
    errors = []
    warnings = []
    
    logger.info(f"🔍 开始验证 {len(updates)} 条更新记录")
    
    # 检查必需字段
    for i, update in enumerate(updates):
        update_prefix = f"更新项 #{i+1}"
        
        # 检查update是否为字典
        if not isinstance(update, dict):
            errors.append(f'{update_prefix}: 数据格式错误，必须是对象')
            continue
        
        # 检查lot_id
        lot_id = update.get('lot_id')
        if not lot_id:
            errors.append(f'{update_prefix}: 缺少内部工单号 (lot_id)')
            continue
        
        if not isinstance(lot_id, str) or lot_id.strip() == '':
            errors.append(f'{update_prefix}: 内部工单号 (lot_id) 格式错误或为空')
            continue
            
        # 验证批次是否存在于 lotprioritydone 表中
        try:
            check_query = text("SELECT LOT_ID FROM lotprioritydone WHERE LOT_ID = :lot_id")
            result = db.session.execute(check_query, {'lot_id': lot_id})
            if not result.fetchone():
                errors.append(f'{update_prefix}: 批次 {lot_id} 在排产表中不存在')
        except Exception as e:
            errors.append(f'{update_prefix}: 验证批次 {lot_id} 时出错: {str(e)}')
            
        # 验证优先级
        priority = update.get('priority')
        if priority is not None:
            if not isinstance(priority, (int, float)):
                errors.append(f'{update_prefix}: 优先级必须是数字格式')
            elif priority < 1 or priority > 999:
                errors.append(f'{update_prefix}: 批次 {lot_id} 的优先级 {priority} 超出范围 (1-999)')
        
        # 验证分选机ID
        handler_id = update.get('handler_id')
        if handler_id is not None and not isinstance(handler_id, str):
            errors.append(f'{update_prefix}: 分选机ID必须是字符串格式')
    
    # 检查优先级重复和连续性
    priority_groups = {}
    for update in updates:
        handler_id = update.get('handler_id')
        priority = update.get('priority')
        lot_id = update.get('lot_id', 'unknown')
        
        if priority is not None and handler_id:
            if handler_id not in priority_groups:
                priority_groups[handler_id] = []
            priority_groups[handler_id].append({
                'priority': priority,
                'lot_id': lot_id
            })
    
    # 检查每个分选机内的优先级是否有重复
    for handler_id, priority_info in priority_groups.items():
        priorities = [info['priority'] for info in priority_info]
        if len(priorities) != len(set(priorities)):
            duplicate_priorities = []
            seen = set()
            for info in priority_info:
                if info['priority'] in seen:
                    duplicate_priorities.append(f"批次{info['lot_id']}(优先级{info['priority']})")
                seen.add(info['priority'])
            errors.append(f'分选机 {handler_id} 存在重复的优先级: {", ".join(duplicate_priorities)}')
    
    validation_result = {
        'valid': len(errors) == 0,
        'message': '; '.join(errors) if errors else '验证通过',
        'errors': errors,
        'warnings': warnings
    }
    
    logger.info(f"✅ 验证完成: {'通过' if validation_result['valid'] else '失败'}, 错误数: {len(errors)}")
    if errors:
        logger.debug(f"📋 验证错误详情: {errors}")
    
    return validation_result

def comprehensive_validation(updates):
    """综合验证调整操作"""
    validation_results = []
    
    # 1. 基础数据验证
    basic_validation = validate_batch_updates(updates)
    validation_results.append({
        'type': 'basic',
        'level': 'error' if not basic_validation['valid'] else 'success',
        'message': basic_validation['message'],
        'details': basic_validation
    })
    
    # 2. 负载平衡检查
    capacity_validation = validate_handler_capacity(updates)
    validation_results.append({
        'type': 'capacity',
        'level': 'warning' if capacity_validation.get('warnings') else 'success',
        'message': '负载检查完成',
        'details': capacity_validation
    })
    
    # 3. 技术匹配度检查
    tech_validation = validate_technical_match(updates)
    validation_results.append({
        'type': 'technical',
        'level': 'info',
        'message': '技术匹配检查完成',
        'details': tech_validation
    })
    
    return validation_results

def validate_handler_capacity(updates):
    """验证分选机负载能力"""
    warnings = []
    handler_loads = {}
    
    # 计算每个分选机的负载
    for update in updates:
        handler_id = update.get('handler_id')
        lot_qty = update.get('quantity', 1000)  # 默认数量
        
        if handler_id and handler_id != 'UNASSIGNED':
            if handler_id not in handler_loads:
                handler_loads[handler_id] = 0
            handler_loads[handler_id] += lot_qty
    
    # 检查负载是否超限（假设每个分选机8小时容量为10000件）
    max_capacity = 500000
    for handler_id, load in handler_loads.items():
        load_percentage = (load / max_capacity) * 100
        
        if load_percentage > 100:
            warnings.append(f'分选机 {handler_id} 负载超限: {load_percentage:.1f}%')
        elif load_percentage > 90:
            warnings.append(f'分选机 {handler_id} 负载较高: {load_percentage:.1f}%')
    
    return {
        'handler_loads': handler_loads,
        'warnings': warnings,
        'max_capacity': max_capacity
    }

def validate_technical_match(updates):
    """验证技术匹配度"""
    warnings = []
    
    # 这里可以添加技术匹配逻辑
    # 例如：检查产品类型与分选机类型的匹配度
    
    for update in updates:
        lot_id = update.get('lot_id')
        handler_id = update.get('handler_id')
        
        # 模拟匹配度检查
        if handler_id and handler_id != 'UNASSIGNED':
            # 这里可以集成实际的匹配算法
            match_score = 85  # 模拟匹配分数
            
            if match_score < 60:
                warnings.append(f'批次 {lot_id} 与分选机 {handler_id} 匹配度较低: {match_score}%')
    
    return {
        'warnings': warnings
    }

def record_adjustment_history(updates, username, operation_type, session_id):
    """记录调整操作历史"""
    try:
        history_record = {
            'timestamp': datetime.utcnow().isoformat(),
            'user': username,
            'operation_type': operation_type,
            'session_id': session_id,
            'updates_count': len(updates),
            'updates': updates[:10] if len(updates) > 10 else updates  # 限制存储的更新记录数量
        }
        
        # 这里可以将历史记录保存到数据库或日志文件
        logger.info(f"📝 记录调整历史: {json.dumps(history_record, ensure_ascii=False)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 记录操作历史失败: {e}")
        return False 