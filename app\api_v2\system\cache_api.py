#!/usr/bin/env python3
"""
缓存管理API - 解决失败批次页面缓存问题
"""

from flask import Blueprint, jsonify, request
from flask_login import login_required
import logging

logger = logging.getLogger(__name__)

cache_bp = Blueprint('cache', __name__)

@cache_bp.route('/clear-page-cache', methods=['POST'])
@login_required
def clear_page_cache():
    """清理页面缓存"""
    try:
        page_name = request.json.get('page_name', 'all')
        
        # 这里可以添加具体的缓存清理逻辑
        # 例如清理Redis缓存、内存缓存等
        
        logger.info(f"🧹 清理页面缓存: {page_name}")
        
        return jsonify({
            'success': True,
            'message': f'页面缓存已清理: {page_name}',
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"清理页面缓存失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@cache_bp.route('/force-refresh', methods=['POST'])
@login_required
def force_refresh():
    """强制刷新指定页面"""
    try:
        page_url = request.json.get('page_url', '')
        
        # 返回强制刷新指令
        return jsonify({
            'success': True,
            'action': 'force_refresh',
            'page_url': page_url,
            'cache_buster': int(time.time()),
            'message': '请刷新页面以获取最新内容'
        })
        
    except Exception as e:
        logger.error(f"强制刷新失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
