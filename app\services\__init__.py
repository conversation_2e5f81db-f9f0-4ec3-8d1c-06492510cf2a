#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Services包初始化文件
只导出核心必要的服务类，避免循环导入问题
"""

# 🔥 导入核心调度服务（手动排产必需）
from .real_scheduling_service import RealSchedulingService
from .data_source_manager import DataSourceManager

# 🔧 尝试导入其他可选服务（允许失败）
try:
    # from .intelligent_scheduling_service import IntelligentSchedulingService  # DISABLED - 统一使用RealSchedulingService
    # 直接重定向到RealSchedulingService
    from .real_scheduling_service import RealSchedulingService
    IntelligentSchedulingService = RealSchedulingService  # 重定向到RealSchedulingService
except ImportError:
    IntelligentSchedulingService = None

# 临时禁用enhanced_data_source_manager（语法错误待修复）
try:
    # from .enhanced_data_source_manager import EnhancedDataSourceManager
    EnhancedDataSourceManager = None
except ImportError:
    EnhancedDataSourceManager = None

try:
    from .universal_excel_parser import UniversalExcelParser
except ImportError:
    UniversalExcelParser = None

try:
    from .enhanced_excel_parser import EnhancedExcelParser
except ImportError:
    EnhancedExcelParser = None

try:
    from .order_excel_parser import OrderExcelParser
except ImportError:
    OrderExcelParser = None

try:
    from .cp_excel_parser import CpExcelParser as CPExcelParser
except ImportError:
    CPExcelParser = None

try:
    from .scheduler_service import scheduler_service
except ImportError:
    scheduler_service = None

try:
    from .background_scheduler_service import background_scheduler
except ImportError:
    background_scheduler = None

# 🏷️ 统一版本号管理中心
# 所有其他模块的版本号都应该从这里导入，确保版本号的一致性
__version__ = '2.3.4'
APP_VERSION = f'{__version__}'  # 带v前缀的版本号（用于展示）
VERSION_INFO = {
    'version': __version__,
    'app_version': APP_VERSION,
    'build_date': '2025-01-16',
    'description': 'AEC-FT智能排产指挥平台'
}

# 对外导出的核心服务列表
__all__ = [
    # 🔥 核心必需服务
    'RealSchedulingService',
    'DataSourceManager',
    
    # 🔧 可选服务（可能为None）
    'IntelligentSchedulingService',
    'EnhancedDataSourceManager',
    'UniversalExcelParser',
    'EnhancedExcelParser',
    'CPExcelParser',
    'OrderExcelParser',
    'scheduler_service',
    'background_scheduler',
    
    # 🏷️ 版本信息
    '__version__',
    'APP_VERSION',
    'VERSION_INFO',
] 