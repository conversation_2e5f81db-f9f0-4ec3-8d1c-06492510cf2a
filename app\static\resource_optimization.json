{"lazy_load": {"echarts": {"condition": "chart_needed", "path": "vendor/echarts/echarts.min.js", "size_kb": 1000}, "xlsx": {"condition": "export_needed", "path": "vendor/xlsx/xlsx.full.min.js", "size_kb": 900}, "fullcalendar": {"condition": "calendar_needed", "path": "vendor/fullcalendar/main.min.js", "size_kb": 200}}, "preload": ["vendor/bootstrap/bootstrap.bundle.min.js", "js/unified_api_client.js", "js/menu-optimizer.js"], "defer": ["js/ai_assistant.js", "js/backend_scheduled_tasks.js"]}