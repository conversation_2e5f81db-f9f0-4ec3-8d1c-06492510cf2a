{% extends "base.html" %}

{% set page_title = "已排产批次" %}
{% set table_title = "已排产批次" %}
{% set page_description = "管理已排产批次信息，包含内部工单号、产品名称、工序、数量、排产时间等。支持筛选、编辑、删除等操作。" %}
{% set table_name = "lotprioritydone" %}
{% set api_endpoint = "/api/v2/production/done-lots" %}

{% block title %}{{ page_title }} - AEC-FT ICP{% endblock %}

{% block extra_css %}
<!-- 引入Bootstrap和FontAwesome -->
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/bootstrap/bootstrap.min.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/all.min.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/custom/theme.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/custom/production-tables.css') }}">

<style>
/* 已排产批次页面特有样式 - 映射到统一样式系统 */
/* 状态样式映射 */
.status-scheduled { 
    background-color: var(--aps-status-info); 
    color: white; 
    padding: 0.2rem 0.5rem; 
    border-radius: 0.25rem; 
    font-size: 0.7rem; 
    font-weight: 500; 
}
.status-running { 
    background-color: var(--aps-status-success); 
    color: white; 
    padding: 0.2rem 0.5rem; 
    border-radius: 0.25rem; 
    font-size: 0.7rem; 
    font-weight: 500; 
}
.status-completed { 
    background-color: var(--aps-score-excellent); 
    color: white; 
    padding: 0.2rem 0.5rem; 
    border-radius: 0.25rem; 
    font-size: 0.7rem; 
    font-weight: 500; 
}
.status-cancelled { 
    background-color: var(--aps-status-danger); 
    color: white; 
    padding: 0.2rem 0.5rem; 
    border-radius: 0.25rem; 
    font-size: 0.7rem; 
    font-weight: 500; 
}

/* 优先级样式使用统一系统 */
.priority-high { 
    color: var(--aps-priority-high); 
    font-weight: bold; 
}
.priority-medium { 
    color: var(--aps-priority-medium); 
    font-weight: bold; 
}
.priority-low { 
    color: var(--aps-priority-low); 
    font-weight: normal; 
}

/* 评分样式使用统一系统 */
.score-high { 
    background-color: var(--aps-score-excellent); 
    color: white; 
    padding: 0.15rem 0.4rem; 
    border-radius: 0.2rem; 
    font-size: 0.65rem; 
    font-weight: bold; 
    display: inline-block; 
    min-width: 2.5rem; 
    text-align: center; 
}
.score-medium { 
    background-color: var(--aps-score-fair); 
    color: white; 
    padding: 0.15rem 0.4rem; 
    border-radius: 0.2rem; 
    font-size: 0.65rem; 
    font-weight: bold; 
    display: inline-block; 
    min-width: 2.5rem; 
    text-align: center; 
}
.score-low { 
    background-color: var(--aps-score-poor); 
    color: white; 
    padding: 0.15rem 0.4rem; 
    border-radius: 0.2rem; 
    font-size: 0.65rem; 
    font-weight: bold; 
    display: inline-block; 
    min-width: 2.5rem; 
    text-align: center; 
}

/* 列样式使用统一系统 */
.time-value { 
    font-family: 'Monaco', 'Consolas', 'Courier New', monospace; 
    font-size: 0.7rem; 
    color: #495057; 
}
.datetime-value { 
    font-size: 0.7rem; 
    color: #6c757d; 
    white-space: nowrap; 
}

.lot-id-column {
    font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
    font-weight: bold;
    color: var(--aps-primary);
    font-size: 0.75rem;
}
.quantity-column { 
    text-align: right; 
    font-weight: bold; 
    font-family: 'Monaco', 'Consolas', 'Courier New', monospace; 
}

.loading-overlay {
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.table-container {
    position: relative;
    min-height: 400px;
}

.badge-priority {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

/* 设备信息样式使用统一系统 */
.handler-info {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 0.25rem;
    padding: 0.2rem 0.5rem;
    font-size: 0.7rem;
    color: #1565c0;
    font-weight: 500;
    display: inline-block;
}

.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
}

.card-header {
    background: linear-gradient(135deg, #b72424 0%, #b72424 100%);
    color: rgb(255, 255, 255);
    font-weight: bold;
}

/* 使用统一表格样式，移除重复定义 */

/* 统一按钮样式 - 与semi-auto页面保持一致 */
.btn-console {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    line-height: 1.5;
    border-radius: 0.375rem;
    white-space: nowrap;
}

.btn-group-sm > .btn, .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.6rem;
}

.stats-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 6px; /* 从8px减少到6px */
    padding: 0.6rem; /* 从1rem减少到0.6rem */
    text-align: center;
    border: 1px solid #dee2e6;
}

.stats-number {
    font-size: 1.5rem; /* 从2rem减少到1.5rem */
    font-weight: bold;
    margin-bottom: 0.15rem; /* 从0.25rem减少到0.15rem */
}

.stats-label {
    color: #6c757d;
    font-size: 0.8rem; /* 从0.875rem减少到0.8rem */
    margin: 0;
}

/* 模式切换样式 */
.mode-toggle-section {
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 1rem;
}

.btn-check:checked + .btn-outline-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

.btn-check:checked + .btn-outline-danger {
    background-color: #b72424;
    border-color: #b72424;
    color: white;
}

/* 调整模式样式 */
.adjustment-workspace {
    min-height: 600px;
}

.handler-groups {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); /* 从400px减少到350px */
    gap: 0.6rem; /* 从1rem减少到0.6rem */
    padding: 0.6rem 0; /* 从1rem减少到0.6rem */
    max-height: calc(100vh - 320px); /* 限制高度，320px为顶部工具栏和底部按钮的高度 */
    overflow-y: auto; /* 只在容器内滚动 */
    overflow-x: hidden; /* 隐藏水平滚动条 */
}

/* 美化滚动条 */
.handler-groups::-webkit-scrollbar {
    width: 8px;
}

.handler-groups::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.handler-groups::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.handler-groups::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.handler-group-card {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background: #fff;
    transition: border-color 0.2s;
    margin-bottom: 0.6rem; /* 从1rem减少到0.6rem */
}

.handler-group-card:hover {
    border-color: #adb5bd;
}

.handler-group-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 0.5rem 0.8rem; /* 从0.75rem 1rem减少到0.5rem 0.8rem */
    font-weight: 500;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #495057;
}

.handler-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.5rem;
    font-weight: bold;
}

.status-idle { background: #d1ecf1; color: #0c5460; }
.status-running { background: #d4edda; color: #155724; }
.status-maintenance { background: #f8d7da; color: #721c24; }
.status-failed { background: #f8d7da; color: #721c24; }

.lot-list-sortable {
    min-height: 150px; /* 从200px减少到150px */
    padding: 0.6rem; /* 从1rem减少到0.6rem */
    background: #fdfdfd;
}

.lot-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 0.4rem; /* 从0.75rem减少到0.4rem */
    margin-bottom: 0.3rem; /* 从0.5rem减少到0.3rem */
    cursor: move;
    transition: border-color 0.2s;
    position: relative;
}

.lot-card:hover {
    border-color: #adb5bd;
}

.lot-card.selected {
    border-color: #0d6efd;
    background: #f8f9ff;
}

.lot-card.drag-over {
    border-color: #28a745;
    background: #f8fff8;
    border-style: dashed;
}

.lot-card.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.lot-priority-badge {
    position: absolute;
    top: -8px;
    left: -8px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
    border: 2px solid white;
}

/* 表格排序功能样式 */
.table-sortable th.sortable {
    position: relative;
    user-select: none;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.table-sortable th.sortable:hover {
    background-color: #e9ecef;
}

.table-sortable th.no-sort {
    cursor: default;
}

.table-sortable th.no-sort:hover {
    background-color: #f8f9fa;
}

/* 排序图标样式 */
.sort-icon {
    transition: all 0.2s ease;
    font-size: 0.75rem;
    margin-left: 0.25rem;
}

.table-sortable th.sorted-asc .sort-icon {
    color: #0d6efd !important;
}

.table-sortable th.sorted-desc .sort-icon {
    color: #0d6efd !important;
}

.table-sortable th.sorted-asc .sort-icon::before {
    content: "\f0de"; /* fa-sort-up */
}

.table-sortable th.sorted-desc .sort-icon::before {
    content: "\f0dd"; /* fa-sort-down */
}

/* 列宽调整手柄 */
.resize-handle {
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: transparent;
    cursor: col-resize;
    border-right: 2px solid transparent;
    transition: border-color 0.2s ease;
    z-index: 10;
}

.resize-handle:hover {
    border-right-color: #0d6efd;
    background: rgba(13, 110, 253, 0.1);
}

.resize-handle.resizing {
    border-right-color: #0d6efd;
    background: rgba(13, 110, 253, 0.2);
}

/* 表格行样式优化 - 参考其他页面使用更小字体 */
.table-sortable tbody tr {
    font-size: 0.75rem; /* 从0.8rem减少到0.75rem */
}

.table-sortable tbody td {
    padding: 0.35rem 0.4rem; /* 稍微减少padding */
    vertical-align: middle;
    border-color: #e9ecef;
    line-height: 1.2; /* 减少行高 */
}

/* 紧凑表格样式 - 更小字体以增加信息密度 */
.compact-table th,
.compact-table td {
    padding: 0.25rem 0.35rem; /* 进一步减少padding */
    font-size: 0.75rem; /* 从0.8rem减少到0.75rem */
    line-height: 1.2; /* 减少行高 */
    border-width: 1px;
}

/* 表格数据特殊样式 */
.compact-table .badge {
    font-size: 0.7rem; /* badge使用更小字体 */
    padding: 0.15rem 0.3rem;
}

.compact-table .btn-sm {
    font-size: 0.7rem; /* 按钮使用更小字体 */
    padding: 0.15rem 0.3rem;
}

.lot-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.lot-details {
    flex: 1;
}

.lot-id {
    font-family: monospace;
    font-weight: bold;
    color: #0d6efd;
    font-size: 0.9rem;
}

.lot-device {
    font-weight: 600;
    color: #333;
    margin: 2px 0;
}

.lot-quantity {
    color: #6c757d;
    font-size: 0.85rem;
}

.lot-actions {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.load-indicator {
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: #e9ecef;
    border-radius: 4px;
    text-align: center;
}

.load-bar {
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    height: 8px;
    margin: 0.25rem 0;
}

.load-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.load-low { background: #28a745; }
.load-medium { background: #ffc107; }
.load-high { background: #fd7e14; }
.load-critical { background: #dc3545; }

/* 拖拽状态样式 */
.sortable-ghost {
    opacity: 0.4;
    background: #e3f2fd !important;
}

.sortable-chosen {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.sortable-drag {
    transform: rotate(5deg);
    opacity: 0.8;
}

/* 操作面板样式 */
.adjustment-panel .card {
    border: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.adjustment-panel .card-header {
    background: #b72424;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    font-size: 0.9rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-item .label {
    font-size: 0.85rem;
    color: #6c757d;
}

.operation-item {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 0.5rem;
    margin-bottom: 0.25rem;
    font-size: 0.8rem;
    border-left: 3px solid #0d6efd;
}

.operation-time {
    color: #6c757d;
    font-size: 0.7rem;
}

/* 失败批次样式 */
.failed-lot {
    border: 2px solid #dc3545 !important;
    background: linear-gradient(135deg, #fff5f5 0%, #ffeaea 100%);
}

.failed-lot:hover {
    border-color: #c82333 !important;
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.failed-priority {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    color: white !important;
    font-size: 10px !important;
    font-weight: bold;
}

.failure-reason {
    margin-top: 4px;
    font-style: italic;
}

/* 列表视图样式 */
.handler-groups.list-view {
    display: block;
}

.handler-groups.list-view .handler-group-card {
    width: 100%;
    margin-bottom: 1rem;
    display: block;
}

.handler-groups.list-view .lot-list-sortable {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.handler-groups.list-view .lot-card {
    width: auto;
    min-width: 200px;
    flex: 0 0 auto;
}

/* 拖拽相关样式 */
.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
}

.handler-group-card.drag-over {
    border-color: #28a745 !important;
    background-color: #f8fff8;
    border-style: dashed !important;
}

/* 批量选择样式 */
.lot-checkbox {
    z-index: 10;
    pointer-events: auto;
}

.lot-checkbox input[type="checkbox"] {
    pointer-events: auto;
    z-index: 11;
    cursor: pointer;
}

.lot-checkbox:hover {
    transform: scale(1.1);
    transition: transform 0.2s ease;
}

/* 优先级更新动画 */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); background-color: #ffeb3b; }
    100% { transform: scale(1); }
}

.lot-card.selected {
    border-color: #0d6efd !important;
    background: #f8f9ff;
}

/* 简化调整工具栏样式 */
.card .card-body.py-2 {
    padding: 0.5rem 1rem !important;
}

/* 优先级更新动画 */
@keyframes pulse {
    0% {
        transform: scale(1);
        background-color: #007bff;
        color: white;
    }
    50% {
        transform: scale(1.1);
        background-color: #0056b3;
        color: white;
    }
    100% {
        transform: scale(1);
        background-color: #007bff;
        color: white;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-tasks me-2"></i>{{ page_title }}
                        </h5>
                        <div>
                            <button type="button" class="btn btn-outline-light btn-console me-2" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                            <button type="button" class="btn btn-outline-light btn-console" onclick="exportData()">
                                <i class="fas fa-file-excel me-1"></i>导出最终调度结果
                            </button>
                        </div>
                        </div>
                    </div>
                    
                <div class="card-body">
                    <!-- 模式切换区域 -->
                    <div class="mode-toggle-section mb-4">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="btn-group" role="group">
                                <input type="radio" class="btn-check" name="viewMode" id="viewModeBtn" checked>
                                <label class="btn btn-outline-primary" for="viewModeBtn">
                                    <i class="fas fa-table me-1"></i>查看模式
                                </label>
                                
                                <input type="radio" class="btn-check" name="viewMode" id="adjustModeBtn">
                                <label class="btn btn-outline-danger" for="adjustModeBtn">
                                    <i class="fas fa-tools me-1"></i>调整模式
                                </label>
                                
                                <input type="radio" class="btn-check" name="viewMode" id="finalResultModeBtn">
                                <label class="btn btn-outline-success" for="finalResultModeBtn">
                                    <i class="fas fa-check-circle me-1"></i>最终结果
                                </label>
                                </div>
                            
                            <div class="mode-actions">
                                <button type="button" class="btn btn-success btn-console me-2" id="saveAdjustmentsBtn" style="display: none;">
                                    <i class="fas fa-save me-1"></i>保存调整
                                        </button>
                                <button type="button" class="btn btn-secondary btn-console" id="cancelAdjustmentsBtn" style="display: none;">
                                    <i class="fas fa-times me-1"></i>取消调整
                                        </button>
                                <button type="button" class="btn btn-primary btn-console me-2" id="publishResultBtn" style="display: none;">
                                    <i class="fas fa-upload me-1"></i>发布到生产
                                        </button>
                                <button type="button" class="btn btn-info btn-console" id="exportFinalResultBtn" style="display: none;">
                                    <i class="fas fa-download me-1"></i>导出调整结果
                                        </button>
                                    </div>
                                </div>
                        </div>
                        
                    <!-- 统计信息 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number text-primary" id="totalRecords">0</div>
                                <p class="stats-label">总记录数</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number text-success" id="totalQuantity">0</div>
                                <p class="stats-label">总数量</p>
                    </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number text-info" id="handlerCount">0</div>
                                <p class="stats-label">分选机数</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number text-warning" id="avgScore">0</div>
                                <p class="stats-label">平均评分</p>
                            </div>
                            </div>
                        </div>
                        
                    <!-- 查看模式内容 -->
                    <div id="viewModeContent" class="mode-content">
                        <!-- 查看模式搜索工具栏 -->
                        <div class="card mb-3">
                            <div class="card-body py-2">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                                            <input type="text" class="form-control" id="viewSearchInput" placeholder="搜索内部工单号、产品名称、分选机ID...">
                                            <button class="btn btn-outline-secondary" type="button" id="clearViewSearchBtn">
                                                <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                                    <div class="col-md-3">
                                        <select class="form-select form-select-sm" id="viewStatusFilter">
                                            <option value="">所有批次</option>
                                            <option value="scheduled">已排产</option>
                                            <option value="failed">排产失败</option>
                                        </select>
                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select form-select-sm" id="viewHandlerFilter">
                                            <option value="">所有分选机</option>
                                </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 批量操作工具栏 -->
                        <div class="batch-operations mb-3" style="display: none;" id="batchOperations">
                            <div class="alert alert-info py-2">
                                <span id="selectedCount">0</span> 条记录已选择
                                <button type="button" class="btn btn-console btn-outline-danger ms-3" onclick="batchDelete()">
                                    <i class="fas fa-trash me-1"></i>批量删除
                                </button>
                                <button type="button" class="btn btn-console btn-outline-primary ms-2" onclick="batchMoveToWaiting()">
                                    <i class="fas fa-arrow-left me-1"></i>移至待排产
                                </button>
                                <button type="button" class="btn btn-console btn-outline-secondary ms-2" onclick="clearSelection()">
                                    <i class="fas fa-times me-1"></i>取消选择
                                </button>
                            </div>
                        </div>
                        
                        <!-- 数据表格 -->
                        <div class="table-container">
                            <div class="loading-overlay" id="loadingOverlay" style="display: none;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                            
                            <div class="aps-table-responsive">
<table class="aps-table table-hover table-sm table-sortable compact-table" id="dataTable">
                                    <thead>
                                    <tr id="tableHeaders">
                                            <th width="50" class="no-sort"><input type="checkbox" id="selectAll"></th>
                                            <th class="sortable" data-column="priority">
                                                <div class="d-flex align-items-center justify-content-between">
                                                    <span>优先级</span>
                                                    <i class="fas fa-sort text-muted sort-icon"></i>
                                                </div>
                                                <div class="resize-handle"></div>
                                            </th>
                                            <th class="sortable" data-column="lot_id">
                                                <div class="d-flex align-items-center justify-content-between">
                                                    <span>内部工单号</span>
                                                    <i class="fas fa-sort text-muted sort-icon"></i>
                                                </div>
                                                <div class="resize-handle"></div>
                                            </th>
                                            <th class="sortable" data-column="device">
                                                <div class="d-flex align-items-center justify-content-between">
                                                    <span>产品名称</span>
                                                    <i class="fas fa-sort text-muted sort-icon"></i>
                                                </div>
                                                <div class="resize-handle"></div>
                                            </th>
                                            <th class="sortable" data-column="handler_id">
                                                <div class="d-flex align-items-center justify-content-between">
                                                    <span>分选机</span>
                                                    <i class="fas fa-sort text-muted sort-icon"></i>
                                                </div>
                                                <div class="resize-handle"></div>
                                            </th>
                                            <th class="sortable" data-column="quantity">
                                                <div class="d-flex align-items-center justify-content-between">
                                                    <span>数量</span>
                                                    <i class="fas fa-sort text-muted sort-icon"></i>
                                                </div>
                                                <div class="resize-handle"></div>
                                            </th>
                                            <th class="sortable" data-column="score">
                                                <div class="d-flex align-items-center justify-content-between">
                                                    <span>评分</span>
                                                    <i class="fas fa-sort text-muted sort-icon"></i>
                                                </div>
                                                <div class="resize-handle"></div>
                                            </th>
                                                                        <th class="sortable" data-column="stage">
                                <div class="d-flex align-items-center justify-content-between">
                                    <span>工序</span>
                                    <i class="fas fa-sort text-muted sort-icon"></i>
                                </div>
                                <div class="resize-handle"></div>
                            </th>
                            <th class="sortable" data-column="step">
                                <div class="d-flex align-items-center justify-content-between">
                                    <span>工步</span>
                                    <i class="fas fa-sort text-muted sort-icon"></i>
                                </div>
                                <div class="resize-handle"></div>
                            </th>
                            <th class="sortable" data-column="pkg_pn">
                                <div class="d-flex align-items-center justify-content-between">
                                    <span>封装类型</span>
                                    <i class="fas fa-sort text-muted sort-icon"></i>
                                </div>
                                <div class="resize-handle"></div>
                            </th>
                                            <th class="sortable" data-column="create_time">
                                                <div class="d-flex align-items-center justify-content-between">
                                                    <span>创建时间</span>
                                                    <i class="fas fa-sort text-muted sort-icon"></i>
                                                </div>
                                                <div class="resize-handle"></div>
                                            </th>
                                            <th width="120" class="no-sort">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="tableBody">
                                    <tr>
                                            <td colspan="12" class="text-center py-4">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">加载中...</span>
                                                </div>
                                                <p class="mt-2 text-muted mb-0">正在加载数据...</p>
                                            </td>
                                    </tr>
                                </tbody>
                            </table>
                            </div>
                        </div>
                        
                        <!-- 分页导航 -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <select class="form-select form-select-sm" style="width: auto;" id="pageSize" onchange="changePageSize()">
                                    <option value="25">25 条/页</option>
                                    <option value="50" selected>50 条/页</option>
                                    <option value="100">100 条/页</option>
                                </select>
                            </div>
                            <nav aria-label="数据分页">
                                <ul class="pagination pagination-sm mb-0" id="pagination">
                                    <!-- 分页按钮动态生成 -->
                            </ul>
                        </nav>
                            <div>
                                <small class="text-muted">
                                    显示第 <span id="startRecord">0</span> - <span id="endRecord">0</span> 条，
                                    共 <span id="totalCount">0</span> 条记录
                                </small>
                    </div>
                </div>
            </div>

                    <!-- 调整模式内容 -->
                    <div id="adjustModeContent" class="mode-content d-none">
                        <!-- 搜索和筛选工具栏 -->
                        <div class="card mb-3">
                            <div class="card-body py-2">
                                <div class="row align-items-center">
                                    <div class="col-md-4">
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                                            <input type="text" class="form-control" id="handlerSearchInput" placeholder="搜索分选机ID或内部工单号...">
                                            <button class="btn btn-outline-secondary" type="button" id="clearSearchBtn">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <select class="form-select form-select-sm" id="handlerStatusFilter">
                                            <option value="">所有状态</option>
                                            <option value="running">运行中</option>
                                            <option value="idle">空闲</option>
                                            <option value="maintenance">维护中</option>
                                            <option value="failed">排产失败</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <select class="form-select form-select-sm" id="loadFilter">
                                            <option value="">所有负载</option>
                                            <option value="low">低负载 (<50%)</option>
                                            <option value="medium">中负载 (50-75%)</option>
                                            <option value="high">高负载 (75-90%)</option>
                                            <option value="critical">超载 (>90%)</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="btn-group w-100" role="group">
                                            <input type="radio" class="btn-check" name="viewLayout" id="gridViewBtn" checked>
                                            <label class="btn btn-outline-secondary btn-console" for="gridViewBtn" title="网格视图">
                                                <i class="fas fa-th-large"></i>
                                            </label>
                                            
                                            <input type="radio" class="btn-check" name="viewLayout" id="listViewBtn">
                                            <label class="btn btn-outline-secondary btn-console" for="listViewBtn" title="列表视图">
                                                <i class="fas fa-list"></i>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="btn-group w-100" role="group">
                                            <button class="btn btn-outline-primary btn-console" onclick="selectAllLots()" title="全选">
                                                <i class="fas fa-check-square"></i>
                                            </button>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-outline-info btn-console dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" title="智能选择">
                                                    <i class="fas fa-magic"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="selectLotsByCondition('failed')">
                                                        <i class="fas fa-exclamation-triangle text-danger me-2"></i>失败批次
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="selectLotsByCondition('high_priority')">
                                                        <i class="fas fa-arrow-up text-warning me-2"></i>高优先级 (≤10)
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="selectLotsByCondition('low_score')">
                                                        <i class="fas fa-chart-line text-info me-2"></i>低评分 (<60)
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="selectLotsByCondition('large_quantity')">
                                                        <i class="fas fa-cubes text-success me-2"></i>大批量 (>50K)
                                                    </a></li>
                                                </ul>
                                            </div>
                                            <button class="btn btn-outline-secondary btn-console" onclick="clearLotSelection()" title="取消选择">
                                                <i class="fas fa-square"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 批量操作工具栏 -->
                        <div class="batch-operations mb-3" style="display: none;" id="adjustBatchOperations">
                            <div class="alert alert-primary py-2 mb-0">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-check-circle me-2"></i>
                                        已选择 <span id="selectedLotCount" class="fw-bold">0</span> 个批次
                                    </div>
                                    <div>
                                        <select class="form-select form-select-sm d-inline-block me-2" style="width: 200px;" id="targetHandlerSelect">
                                            <option value="">选择目标分选机...</option>
                                        </select>
                                        <button type="button" class="btn btn-console btn-success me-2" onclick="batchMoveToHandler()">
                                            <i class="fas fa-arrow-right me-1"></i>批量移动
                                        </button>
                                        <button type="button" class="btn btn-console btn-warning me-2" onclick="batchChangePriority()">
                                            <i class="fas fa-sort-numeric-up me-1"></i>批量调优先级
                                        </button>
                                        <button type="button" class="btn btn-console btn-danger me-2" onclick="batchDeleteLots()">
                                            <i class="fas fa-trash me-1"></i>批量删除
                                        </button>
                                        <button type="button" class="btn btn-console btn-info me-2" onclick="batchResetToFailed()">
                                            <i class="fas fa-exclamation-triangle me-1"></i>批量标记失败
                                        </button>
                                        <button type="button" class="btn btn-console btn-outline-secondary" onclick="clearLotSelection()">
                                            <i class="fas fa-times me-1"></i>取消选择
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="adjustment-workspace">
                            <div class="row">
                                <!-- 分选机分组区域 -->
                                <div class="col-xl-9 col-lg-8">
                                    <div id="handlerGroupsContainer" class="handler-groups">
                                        <!-- 动态生成分选机分组卡片 -->
                                        <div class="text-center py-5">
                                            <div class="spinner-border text-danger" role="status">
                                                <span class="visually-hidden">正在加载分组数据...</span>
                                            </div>
                                            <p class="mt-2 text-muted">正在按分选机分组数据...</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 右侧操作面板 -->
                                <div class="col-xl-3 col-lg-4">
                                    <div class="adjustment-panel">
                                        <!-- 调整统计卡片 -->
                                        <div class="card mb-3">
                                            <div class="card-header">
                                                <i class="fas fa-chart-bar me-2"></i>调整统计
                                            </div>
                                            <div class="card-body">
                                                <div class="stat-item mb-2">
                                                    <span class="label">总批次数:</span>
                                                    <span class="value fw-bold text-primary" id="adjustTotalLots">0</span>
                                                </div>
                                                <div class="stat-item mb-2">
                                                    <span class="label">分选机数:</span>
                                                    <span class="value fw-bold text-info" id="adjustTotalHandlers">0</span>
                                                </div>
                                                <div class="stat-item mb-2">
                                                    <span class="label">平均负载:</span>
                                                    <span class="value fw-bold text-warning" id="adjustAverageLoad">0%</span>
                                                </div>
                                                <div class="stat-item mb-2">
                                                    <span class="label">已修改:</span>
                                                    <span class="value fw-bold text-danger" id="modifiedLots">0</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 操作历史卡片 -->
                                        <div class="card">
                                            <div class="card-header">
                                                <i class="fas fa-history me-2"></i>操作历史
                                            </div>
                                            <div class="card-body">
                                                <div id="operationHistory" class="operation-list" style="max-height: 200px; overflow-y: auto;">
                                                    <div class="text-center text-muted">
                                                        <i class="fas fa-clock"></i>
                                                        <small>暂无操作记录</small>
                                                    </div>
                                                </div>
                                                <div class="history-actions mt-2">
                                                    <button class="btn btn-sm btn-outline-secondary me-1" id="undoBtn" disabled>
                                                        <i class="fas fa-undo"></i> 撤销
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-secondary" id="redoBtn" disabled>
                                                        <i class="fas fa-redo"></i> 重做
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 最终结果模式内容 -->
                    <div id="finalResultModeContent" class="mode-content d-none">
                        <!-- 最终结果搜索工具栏 -->
                        <div class="card mb-3">
                            <div class="card-body py-2">
                                <div class="row align-items-center">
                                    <div class="col-md-4">
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                                            <input type="text" class="form-control" id="finalResultSearchInput" placeholder="搜索内部工单号、产品名称...">
                                            <button class="btn btn-outline-secondary" type="button" id="clearFinalResultSearchBtn">
                                                <i class="fas fa-times"></i>
                                            </button>
                </div>
            </div>
                                    <div class="col-md-3">
                                        <select class="form-select form-select-sm" id="finalResultSessionFilter">
                                            <option value="">选择调整会话...</option>
                                        </select>
        </div>
                                    <div class="col-md-3">
                                        <select class="form-select form-select-sm" id="finalResultHandlerFilter">
                                            <option value="">所有分选机</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-sm btn-outline-primary" onclick="loadFinalResultData()">
                                            <i class="fas fa-refresh me-1"></i>刷新
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 最终结果表格 -->
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        <i class="fas fa-check-circle me-2"></i>最终排产调整结果
                                    </h6>
                                    <div>
                                        <span class="badge bg-success" id="finalResultSessionStatus">草稿</span>
                                        <span class="badge bg-info ms-2" id="finalResultSessionName">-</span>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="aps-table table-hover table-sm table-sortable compact-table" id="finalResultDataTable">
                                        <thead>
                                            <tr id="finalResultTableHeaders">
                                                <th width="50" class="no-sort">
                                                    <input type="checkbox" id="selectAllFinalResult">
                                                </th>
                                                <th class="sortable" data-column="lot_id">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <span>内部工单号</span>
                                                        <i class="fas fa-sort text-muted sort-icon"></i>
                                                    </div>
                                                    <div class="resize-handle"></div>
                                                </th>
                                                <th class="sortable" data-column="device">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <span>产品名称</span>
                                                        <i class="fas fa-sort text-muted sort-icon"></i>
                                                    </div>
                                                    <div class="resize-handle"></div>
                                                </th>
                                                <th class="sortable" data-column="quantity">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <span>数量</span>
                                                        <i class="fas fa-sort text-muted sort-icon"></i>
                                                    </div>
                                                    <div class="resize-handle"></div>
                                                </th>
                                                <th class="sortable" data-column="final_handler_id">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <span>最终分选机</span>
                                                        <i class="fas fa-sort text-muted sort-icon"></i>
                                                    </div>
                                                    <div class="resize-handle"></div>
                                                </th>
                                                <th class="sortable" data-column="final_priority">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <span>最终优先级</span>
                                                        <i class="fas fa-sort text-muted sort-icon"></i>
                                                    </div>
                                                    <div class="resize-handle"></div>
                                                </th>
                                                <th class="sortable" data-column="source_type">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <span>来源</span>
                                                        <i class="fas fa-sort text-muted sort-icon"></i>
                                                    </div>
                                                    <div class="resize-handle"></div>
                                                </th>
                                                <th class="sortable" data-column="adjustment_type">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <span>调整类型</span>
                                                        <i class="fas fa-sort text-muted sort-icon"></i>
                                                    </div>
                                                    <div class="resize-handle"></div>
                                                </th>
                                                <th class="sortable" data-column="original_priority">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <span>原优先级</span>
                                                        <i class="fas fa-sort text-muted sort-icon"></i>
                                                    </div>
                                                    <div class="resize-handle"></div>
                                                </th>
                                                <th class="sortable" data-column="original_handler_id">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <span>原分选机</span>
                                                        <i class="fas fa-sort text-muted sort-icon"></i>
                                                    </div>
                                                    <div class="resize-handle"></div>
                                                </th>
                                                <th class="sortable" data-column="adjusted_by">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <span>调整人</span>
                                                        <i class="fas fa-sort text-muted sort-icon"></i>
                                                    </div>
                                                    <div class="resize-handle"></div>
                                                </th>
                                                <th class="sortable" data-column="adjusted_at">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <span>调整时间</span>
                                                        <i class="fas fa-sort text-muted sort-icon"></i>
                                                    </div>
                                                    <div class="resize-handle"></div>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody id="finalResultTableBody">
                                            <tr>
                                                <td colspan="12" class="text-center py-5">
                                                    <div class="spinner-border text-primary" role="status">
                                                        <span class="visually-hidden">正在加载最终结果数据...</span>
                                                    </div>
                                                    <p class="mt-2 text-muted">正在加载最终排产调整结果...</p>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 最终结果分页 -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <nav>
                                <ul class="pagination pagination-sm mb-0" id="finalResultPagination">
                                    <!-- 分页按钮动态生成 -->
                                </ul>
                            </nav>
                            <div>
                                <small class="text-muted">
                                    显示第 <span id="finalResultStartRecord">0</span> - <span id="finalResultEndRecord">0</span> 条，
                                    共 <span id="finalResultTotalCount">0</span> 条记录
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 引入必要的JavaScript库 -->
<script src="{{ url_for('static', filename='vendor/bootstrap/bootstrap.bundle.min.js') }}"></script>
<script src="{{ url_for('static', filename='vendor/xlsx/xlsx.full.min.js') }}"></script>

<script>
// 页面配置
const API_ENDPOINT = '{{ api_endpoint }}';
const TABLE_NAME = '{{ table_name }}';

// 全局变量
let currentData = [];
let currentPage = 1;
let pageSize = 50;
let totalRecords = 0;
let totalPages = 0;
let isLoading = false;

// 调整模式变量
let currentMode = 'view';
let adjustmentManager = null;
let groupedData = {};
let operationHistory = [];

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('📋 已排产批次页面初始化开始');
    console.log('🔗 API端点:', API_ENDPOINT);
    console.log('📊 表名:', TABLE_NAME);
    
    // 验证关键DOM元素是否存在
    const requiredElements = ['tableBody', 'tableHeaders', 'pagination', 'totalRecords'];
    const missingElements = requiredElements.filter(id => !document.getElementById(id));
    
    if (missingElements.length > 0) {
        console.error('❌ 缺少必要的DOM元素:', missingElements);
            return;
        }
        
    // 初始化事件监听
    initializeEventListeners();
    
    // 初始化调整管理器
    adjustmentManager = new LotAdjustmentManager();
    
    // 检查URL参数，决定初始模式
    const urlParams = new URLSearchParams(window.location.search);
    const targetMode = urlParams.get('mode');
    
    if (targetMode === 'adjust') {
        console.log('🎯 检测到调整模式参数，准备切换到调整模式');
        // 调整模式：先加载包含失败批次的数据，再切换到调整模式
        document.getElementById('adjustModeBtn').checked = true;
        setTimeout(() => {
            loadData(1, true).then(() => {
                console.log('✅ 调整模式数据加载完成，切换到调整模式');
                switchToAdjustMode();
            }).catch(error => {
                console.error('❌ 调整模式数据加载失败:', error);
                showNotification('error', '调整模式数据加载失败，将切换到查看模式');
                // 回退到查看模式
                document.getElementById('viewModeBtn').checked = true;
                setTimeout(() => {
                    loadData();
                }, 500);
            });
        }, 100);
    } else if (targetMode === 'final_result') {
        console.log('🎯 检测到最终结果模式参数，准备切换到最终结果模式');
        // 最终结果模式：直接切换到最终结果模式
        document.getElementById('finalResultModeBtn').checked = true;
        setTimeout(() => {
            switchToFinalResultMode();
        }, 100);
    } else {
        console.log('🎯 默认模式，加载普通数据');
        // 查看模式：只加载成功的批次数据
        setTimeout(() => {
            loadData().then(() => {
        console.log('✅ 查看模式数据加载完成');
    }).catch(error => {
        console.error('❌ 查看模式数据加载失败:', error);
        showNotification('error', '数据加载失败，请刷新页面重试');
    });
        }, 100);
    }
    
    console.log('✅ 页面初始化完成');
});

// 初始化事件监听器
function initializeEventListeners() {
    // 全选复选框
    const selectAllEl = document.getElementById('selectAll');
    if (selectAllEl) {
        selectAllEl.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.row-checkbox, .lot-selector');
            checkboxes.forEach(cb => cb.checked = this.checked);
            updateLotSelection(); // 统一使用 updateLotSelection 函数
        });
    }

    // 监听行复选框变化（统一处理row-checkbox和lot-selector）
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('row-checkbox') || e.target.classList.contains('lot-selector')) {
            e.stopPropagation(); // 防止冒泡到拖拽事件
            updateLotSelection(); // 统一使用 updateLotSelection 函数
        }
    });
    
    // 为复选框添加点击事件，防止触发拖拽
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('row-checkbox') || e.target.classList.contains('lot-selector')) {
            e.stopPropagation(); // 防止触发拖拽
        }
    });

    // 模式切换事件监听
    const viewModeBtn = document.getElementById('viewModeBtn');
    const adjustModeBtn = document.getElementById('adjustModeBtn');
    const finalResultModeBtn = document.getElementById('finalResultModeBtn');
    
    if (viewModeBtn) {
        viewModeBtn.addEventListener('change', function() {
            if (this.checked) {
                switchToViewMode();
            }
        });
    }
    
    if (adjustModeBtn) {
        adjustModeBtn.addEventListener('change', function() {
            if (this.checked) {
                switchToAdjustMode();
            }
        });
    }
    
    if (finalResultModeBtn) {
        finalResultModeBtn.addEventListener('change', function() {
            if (this.checked) {
                switchToFinalResultMode();
            }
        });
    }

    // 调整模式操作按钮
    const saveBtn = document.getElementById('saveAdjustmentsBtn');
    const cancelBtn = document.getElementById('cancelAdjustmentsBtn');
    const undoBtn = document.getElementById('undoBtn');
    const redoBtn = document.getElementById('redoBtn');

    if (saveBtn) {
        saveBtn.addEventListener('click', saveAdjustments);
    }
    if (cancelBtn) {
        cancelBtn.addEventListener('click', cancelAdjustments);
    }
    if (undoBtn) {
        undoBtn.addEventListener('click', () => adjustmentManager.undo());
    }
    if (redoBtn) {
        redoBtn.addEventListener('click', () => adjustmentManager.redo());
    }

    // 最终结果模式操作按钮
    const publishBtn = document.getElementById('publishResultBtn');
    const exportBtn = document.getElementById('exportFinalResultBtn');

    if (publishBtn) {
        publishBtn.addEventListener('click', publishFinalResult);
    }
    if (exportBtn) {
        exportBtn.addEventListener('click', exportFinalResult);
    }

    // 最终结果模式筛选功能
    const finalResultSearchInput = document.getElementById('finalResultSearchInput');
    const clearFinalResultSearchBtn = document.getElementById('clearFinalResultSearchBtn');
    const finalResultSessionFilter = document.getElementById('finalResultSessionFilter');
    const finalResultHandlerFilter = document.getElementById('finalResultHandlerFilter');

    // 最终结果搜索输入事件
    if (finalResultSearchInput) {
        let searchTimeout;
        finalResultSearchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                loadFinalResultData(1); // 重置到第一页
            }, 300); // 防抖延迟300ms
        });
    }

    // 清除搜索按钮
    if (clearFinalResultSearchBtn) {
        clearFinalResultSearchBtn.addEventListener('click', function() {
            if (finalResultSearchInput) {
                finalResultSearchInput.value = '';
                loadFinalResultData(1); // 重置到第一页
            }
        });
    }

    // 会话筛选
    if (finalResultSessionFilter) {
        finalResultSessionFilter.addEventListener('change', function() {
            loadFinalResultData(1); // 重置到第一页
        });
    }

    // 分选机筛选
    if (finalResultHandlerFilter) {
        finalResultHandlerFilter.addEventListener('change', function() {
            loadFinalResultData(1); // 重置到第一页
        });
    }

    // 查看模式搜索功能
    const viewSearchInput = document.getElementById('viewSearchInput');
    const clearViewSearchBtn = document.getElementById('clearViewSearchBtn');
    const viewStatusFilter = document.getElementById('viewStatusFilter');
    const viewHandlerFilter = document.getElementById('viewHandlerFilter');

    // 查看模式搜索输入事件
    if (viewSearchInput) {
        let searchTimeout;
        viewSearchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                applyViewModeFilters();
            }, 300); // 防抖延迟300ms
        });
    }

    // 清除搜索按钮
    if (clearViewSearchBtn) {
        clearViewSearchBtn.addEventListener('click', function() {
            if (viewSearchInput) {
                viewSearchInput.value = '';
                applyViewModeFilters();
            }
        });
    }

    // 状态筛选
    if (viewStatusFilter) {
        viewStatusFilter.addEventListener('change', applyViewModeFilters);
    }

    // 分选机筛选
    if (viewHandlerFilter) {
        viewHandlerFilter.addEventListener('change', applyViewModeFilters);
    }
}

// 加载数据（为调整模式增强，包含失败批次）
function loadData(page = 1, includeFailedLots = false) {
    if (isLoading) return Promise.reject('正在加载中...');
    
    console.log(`🔄 开始加载第 ${page} 页数据 ${includeFailedLots ? '(包含失败批次)' : ''}`);
    
    isLoading = true;
    currentPage = page;
    showLoading(true);
    
    if (includeFailedLots) {
        // 调整模式：同时获取成功和失败的批次
        return loadDataWithFailedLots();
    }
    
    // 查看模式：只获取成功的批次
    return loadSuccessfulLots(page);
}

// 加载成功的批次（原有逻辑）
function loadSuccessfulLots(page) {
    // 构建API请求参数 - 使用正确的参数格式
    const params = new URLSearchParams({
        table: TABLE_NAME,
        page: page,
        size: pageSize
    });
    
    const url = `${API_ENDPOINT}?${params}`;
    console.log('📡 请求URL:', url);
    
    return fetch(url, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        },
        credentials: 'same-origin'
    })
    .then(response => {
        console.log('📡 响应状态:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('📊 API返回数据:', data);
        
        if (data.success) {
            currentData = data.data || [];
            
            // 兼容不同的分页格式
            if (data.pagination) {
                totalRecords = data.pagination.total || 0;
                totalPages = data.pagination.total_pages || 0;
        } else {
                totalRecords = data.total || 0;
                totalPages = Math.ceil(totalRecords / pageSize) || 0;
            }
            
            console.log(`✅ 数据加载成功: ${currentData.length} 条记录，共 ${totalRecords} 条`);
            
            renderTable();
            renderPagination();
            updateStatistics();
            
            // 初始化查看模式的分选机筛选下拉框
            if (currentMode === 'view') {
                updateHandlerFilterOptions(currentData);
            }
            
            return currentData;
        } else {
            throw new Error(data.message || data.error || '数据加载失败');
        }
    })
    .catch(error => {
        console.error('❌ 数据加载失败:', error);
        showError(error.message);
        throw error;
    })
    .finally(() => {
        isLoading = false;
        showLoading(false);
    });
}

// 加载包含失败批次的数据（调整模式专用）
function loadDataWithFailedLots() {
    // 获取当前排产会话的成功和失败数据
    const successPromise = fetch(`${API_ENDPOINT}?table=${TABLE_NAME}&page=1&size=10000`);
    
    // 获取当前排产会话的失败批次 - 添加时间筛选
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0]; // YYYY-MM-DD格式
    const failedPromise = fetch(`/api/v2/production/get-failed-lots-from-logs?current_only=true&hours=24`);
    
    return Promise.all([successPromise, failedPromise])
        .then(async ([successResponse, failedResponse]) => {
            console.log('📡 成功和失败批次API响应状态:', {
                success: successResponse.status,
                failed: failedResponse.status
            });
            
            // 检查响应状态
            if (!successResponse.ok) {
                throw new Error(`获取成功批次失败: HTTP ${successResponse.status}`);
            }
            if (!failedResponse.ok) {
                console.warn(`获取失败批次失败: HTTP ${failedResponse.status}，将继续处理成功批次`);
            }
            
            const successData = await successResponse.json();
            const failedData = failedResponse.ok ? await failedResponse.json() : { success: false, data: { failed_lots: [] } };
            
            console.log('📊 API数据:', { successData, failedData });
            
            if (!successData.success) {
                throw new Error(successData.message || '获取成功批次失败');
            }
            
            const successLots = successData.data || [];
            const failedLots = failedData.success ? (failedData.data.failed_lots || []) : [];
            
            // 标记失败数据，设置为UNASSIGNED分选机
            const markedFailedLots = failedLots.map(item => ({
                ...item,
                status: 'failed',
                HANDLER_ID: 'UNASSIGNED',  // 失败批次统一放入未分配组
                handler_id: 'UNASSIGNED',
                PRIORITY: 999,  // 失败批次设置为最低优先级
                priority: 999,
                failure_reason: item.failure_reason || '排产失败',
                is_failed: true,
                // 统一字段命名，确保兼容性
                LOT_ID: item.LOT_ID || item.lot_id || `FAILED_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                DEVICE: item.DEVICE || item.device || '未知产品',
                STAGE: item.STAGE || item.stage || 'FT',
                GOOD_QTY: item.GOOD_QTY || item.good_qty || item.lot_qty || 0,
                WIP_STATE: 'FAILED',
                CREATE_TIME: item.CREATE_TIME || item.failure_time || new Date().toISOString()
            }));
            
            // 合并数据
            currentData = [...successLots, ...markedFailedLots];
            totalRecords = currentData.length;
            totalPages = 1; // 调整模式不分页
            
            console.log(`✅ 调整模式数据加载成功: 成功批次 ${successLots.length}, 失败批次 ${failedLots.length}`);
            
            // 渲染数据到界面
                        renderTable();
                        updateStatistics();
                        
            // 如果在调整模式，通知管理器更新数据
            if (adjustmentManager) {
                adjustmentManager.updateData(currentData);
            }
            
            return currentData;
        })
        .catch(error => {
            console.error('❌ 调整模式数据加载失败:', error);
            showError(error.message);
            throw error;
        })
        .finally(() => {
            isLoading = false;
            showLoading(false);
        });
}

// 查看模式的过滤功能
function applyViewModeFilters() {
    if (currentMode !== 'view') return;
    
    const searchInput = document.getElementById('viewSearchInput');
    const statusFilter = document.getElementById('viewStatusFilter');
    const handlerFilter = document.getElementById('viewHandlerFilter');
    
    // 获取过滤条件
    const searchTerm = searchInput ? searchInput.value.trim().toLowerCase() : '';
    const statusValue = statusFilter ? statusFilter.value : '';
    const handlerValue = handlerFilter ? handlerFilter.value : '';
    
    // 从原始数据开始过滤
    let filteredData = [...currentData];
    
    // 文本搜索
    if (searchTerm) {
        filteredData = filteredData.filter(item => {
            const lotId = (item.LOT_ID || '').toLowerCase();
            const device = (item.DEVICE || '').toLowerCase();
            const handlerId = (item.HANDLER_ID || '').toLowerCase();
            const chipId = (item.CHIP_ID || '').toLowerCase();
            
            return lotId.includes(searchTerm) || 
                   device.includes(searchTerm) || 
                   handlerId.includes(searchTerm) ||
                   chipId.includes(searchTerm);
        });
    }
    
    // 状态筛选
    if (statusValue) {
        filteredData = filteredData.filter(item => {
            const status = (item.WIP_STATE || '').toLowerCase();
            if (statusValue === 'scheduled') {
                return status === 'scheduled' || status === 'done';
            } else if (statusValue === 'failed') {
                return status === 'failed' || item.is_failed;
            }
            return true;
        });
    }
    
    // 分选机筛选
    if (handlerValue) {
        filteredData = filteredData.filter(item => {
            return (item.HANDLER_ID || '') === handlerValue;
        });
    }
    
    // 临时替换currentData进行渲染
    const originalData = currentData;
    currentData = filteredData;
    
    // 重新渲染表格
    renderTable();
    
    // 更新统计信息
    updateFilteredStatistics(filteredData, originalData.length);
    
    // 恢复原始数据
    currentData = originalData;
    
    console.log(`🔍 查看模式过滤完成: ${filteredData.length}/${originalData.length} 条记录`);
}

// 更新过滤后的统计信息
function updateFilteredStatistics(filteredData, originalTotal) {
    const totalQuantity = filteredData.reduce((sum, row) => sum + (row.GOOD_QTY || 0), 0);
    const handlers = new Set(filteredData.map(row => row.HANDLER_ID).filter(id => id));
    const totalScore = filteredData.reduce((sum, row) => sum + (row.comprehensive_score || 0), 0);
    const avgScore = filteredData.length > 0 ? totalScore / filteredData.length : 0;
    
    // 更新统计信息显示
    const totalRecordsEl = document.getElementById('totalRecords');
    if (totalRecordsEl) {
        if (filteredData.length < originalTotal) {
            totalRecordsEl.innerHTML = `${filteredData.length} <small class="text-muted">/ ${originalTotal}</small>`;
        } else {
            totalRecordsEl.textContent = filteredData.length;
        }
    }
    
    const totalQuantityEl = document.getElementById('totalQuantity');
    if (totalQuantityEl) totalQuantityEl.textContent = formatNumber(totalQuantity);
    
    const handlerCountEl = document.getElementById('handlerCount');
    if (handlerCountEl) handlerCountEl.textContent = handlers.size;
    
    const avgScoreEl = document.getElementById('avgScore');
    if (avgScoreEl) avgScoreEl.textContent = avgScore.toFixed(1);
    
    // 更新分选机下拉框选项
    updateHandlerFilterOptions(filteredData);
}

// 更新分选机筛选下拉框的选项
function updateHandlerFilterOptions(data) {
    const handlerFilter = document.getElementById('viewHandlerFilter');
    if (!handlerFilter) return;
    
    const currentValue = handlerFilter.value;
    const handlers = [...new Set(data.map(item => item.HANDLER_ID).filter(id => id))].sort();
    
    let html = '<option value="">所有分选机</option>';
    handlers.forEach(handlerId => {
        const count = data.filter(item => item.HANDLER_ID === handlerId).length;
        html += `<option value="${handlerId}">${handlerId} (${count})</option>`;
    });
    
    handlerFilter.innerHTML = html;
    handlerFilter.value = currentValue; // 保持当前选择
}

// 渲染表格
function renderTable() {
    const tbody = document.getElementById('tableBody');
    
    if (!tbody) {
        console.error('❌ 找不到tableBody元素');
        return;
    }
    
    if (currentData.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="12" class="text-center py-4">
                    <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">暂无数据</p>
                </td>
            </tr>
        `;
        return;
    }
    
    let html = '';
    currentData.forEach((row, index) => {
        html += `
            <tr data-id="${row.id || row.PRIORITY || index}">
                <td>
                    <input type="checkbox" class="row-checkbox" value="${row.id || row.PRIORITY || index}">
                </td>
                <td>
                    <span class="badge badge-priority ${getPriorityClass(row.PRIORITY)}">${row.PRIORITY || 'N/A'}</span>
                </td>
                <td>
                    <span class="lot-id-column">${row.LOT_ID || 'N/A'}</span>
                </td>
                <td>
                    <strong>${row.DEVICE || 'N/A'}</strong>
                    ${row.CHIP_ID ? `<br><small class="text-muted">${row.CHIP_ID}</small>` : ''}
                </td>
                <td>
                    <span class="handler-info">${row.HANDLER_ID || 'N/A'}</span>
                </td>
                <td>
                    <span class="quantity-column">${formatNumber(row.GOOD_QTY || 0)}</span>
                </td>
                <td>
                    <span class="${getScoreClass(row.comprehensive_score)}">${formatScore(row.comprehensive_score)}</span>
                </td>
                <td>
                    <span class="badge bg-secondary">${row.STAGE || 'N/A'}</span>
                </td>
                <td>
                    <span class="badge bg-info">${row.STEP || 'N/A'}</span>
                </td>
                <td>
                    <span class="text-primary fw-medium">${row.PKG_PN || 'N/A'}</span>
                </td>
                <td>
                    <span class="datetime-value">${formatDateTime(row.CREATE_TIME)}</span>
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editRecord(${row.id || index})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteRecord(${row.id || index})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    });
    
    tbody.innerHTML = html;
}

// 渲染分页
function renderPagination() {
    const pagination = document.getElementById('pagination');
    
    if (!pagination) {
        console.error('❌ 找不到pagination元素');
        return;
    }
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let html = '';
    
    // 上一页
    if (currentPage > 1) {
        html += `<li class="page-item">
            <a class="page-link" href="#" onclick="loadData(${currentPage - 1}); return false;">上一页</a>
        </li>`;
    }
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        html += `<li class="page-item ${i === currentPage ? 'active' : ''}">
            <a class="page-link" href="#" onclick="loadData(${i}); return false;">${i}</a>
        </li>`;
    }
    
    // 下一页
    if (currentPage < totalPages) {
        html += `<li class="page-item">
            <a class="page-link" href="#" onclick="loadData(${currentPage + 1}); return false;">下一页</a>
        </li>`;
    }
    
    pagination.innerHTML = html;
    
    // 更新记录信息
    const startRecord = (currentPage - 1) * pageSize + 1;
    const endRecord = Math.min(currentPage * pageSize, totalRecords);
    
    const startRecordEl = document.getElementById('startRecord');
    if (startRecordEl) startRecordEl.textContent = startRecord;
    
    const endRecordEl = document.getElementById('endRecord');
    if (endRecordEl) endRecordEl.textContent = endRecord;
    
    const totalCountEl = document.getElementById('totalCount');
    if (totalCountEl) totalCountEl.textContent = totalRecords;
}

// 更新统计信息
function updateStatistics() {
    const totalQuantity = currentData.reduce((sum, row) => sum + (row.GOOD_QTY || 0), 0);
    const handlers = new Set(currentData.map(row => row.HANDLER_ID).filter(id => id));
    const totalScore = currentData.reduce((sum, row) => sum + (row.comprehensive_score || 0), 0);
    const avgScore = currentData.length > 0 ? totalScore / currentData.length : 0;
    
    // 安全地更新统计信息，防止null引用错误
    const totalRecordsEl = document.getElementById('totalRecords');
    if (totalRecordsEl) totalRecordsEl.textContent = totalRecords;
    
    const totalQuantityEl = document.getElementById('totalQuantity');
    if (totalQuantityEl) totalQuantityEl.textContent = formatNumber(totalQuantity);
    
    const handlerCountEl = document.getElementById('handlerCount');
    if (handlerCountEl) handlerCountEl.textContent = handlers.size;
    
    const avgScoreEl = document.getElementById('avgScore');
    if (avgScoreEl) avgScoreEl.textContent = avgScore.toFixed(1);
}

// 显示/隐藏加载状态
function showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.style.display = show ? 'flex' : 'none';
    }
}

// 显示错误信息
function showError(message) {
    const tbody = document.getElementById('tableBody');
    if (!tbody) {
        console.error('❌ 找不到tableBody元素，无法显示错误信息');
        return;
    }
    
    tbody.innerHTML = `
        <tr>
                            <td colspan="12" class="text-center py-4">
                <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                <p class="text-danger mb-0">${message}</p>
                <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadData()">
                    <i class="fas fa-redo me-1"></i>重试
                </button>
            </td>
        </tr>
    `;
}

// 工具函数
function getPriorityClass(priority) {
    if (priority >= 8) return 'bg-danger';
    if (priority >= 1) return 'bg-warning';
    return 'bg-success';
}

function getScoreClass(score) {
    const s = parseFloat(score) || 0;
    if (s >= 80) return 'score-high';
    if (s >= 60) return 'score-medium';
    return 'score-low';
}

function getStatusClass(status) {
    switch(status) {
        case 'READY': return 'bg-success';
        case 'RUNNING': return 'bg-primary';
        case 'WAIT': return 'bg-warning';
        case 'HOLD': return 'bg-danger';
        default: return 'bg-secondary';
    }
}

function formatNumber(num) {
    return num.toLocaleString();
}

function formatScore(score) {
    return score ? parseFloat(score).toFixed(1) : '0.0';
}

function formatDateTime(dateStr) {
    if (!dateStr) return 'N/A';
    try {
        const date = new Date(dateStr);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    } catch (e) {
        return dateStr;
    }
}

// 批量操作
function updateBatchOperations() {
    const selectedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
    const batchOps = document.getElementById('batchOperations');
    const selectedCount = document.getElementById('selectedCount');
    
    if (selectedCheckboxes.length > 0) {
        if (batchOps) batchOps.style.display = 'block';
        if (selectedCount) selectedCount.textContent = selectedCheckboxes.length;
    } else {
        if (batchOps) batchOps.style.display = 'none';
    }
}

function clearSelection() {
    document.querySelectorAll('input[type="checkbox"]').forEach(cb => cb.checked = false);
    updateBatchOperations();
}

function getSelectedIds() {
    const selectedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
    return Array.from(selectedCheckboxes).map(cb => cb.value);
}

// 操作函数
function refreshData() {
    loadData(currentPage);
}

function changePageSize() {
    const select = document.getElementById('pageSize');
    pageSize = parseInt(select.value);
    loadData(1);
}

function editRecord(id) {
    showNotification('info', '编辑功能开发中...');
}

function deleteRecord(id) {
    if (confirm('确定要删除这条记录吗？')) {
        showNotification('info', '删除功能开发中...');
    }
}

function batchDelete() {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) {
        showNotification('warning', '请选择要删除的记录');
        return;
    }
    
    if (confirm(`确定要删除选中的 ${selectedIds.length} 条记录吗？`)) {
        showNotification('info', '批量删除功能开发中...');
    }
}

function batchMoveToWaiting() {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) {
        showNotification('warning', '请选择要移动的记录');
        return;
    }
    
    if (confirm(`确定要将选中的 ${selectedIds.length} 条记录移至待排产吗？`)) {
        showNotification('info', '移动功能开发中...');
    }
}

function exportData() {
    if (currentData.length === 0) {
        showNotification('warning', '没有数据可导出');
        return;
    }
    
    try {
        const exportData = currentData.map(row => ({
            '优先级': row.PRIORITY,
            '内部工单号': row.LOT_ID,
            '产品名称': row.DEVICE,
            '芯片名称': row.CHIP_ID,
            '分选机ID': row.HANDLER_ID,
            '良品数量': row.GOOD_QTY,
            '综合评分': row.comprehensive_score,
            '工序': row.STAGE,
            '工步': row.STEP,
            '封装类型': row.PKG_PN,
            '创建时间': row.CREATE_TIME
        }));
        
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(exportData);
        XLSX.utils.book_append_sheet(wb, ws, "已排产批次");
        
        const filename = `已排产批次_${new Date().toISOString().slice(0, 10)}.xlsx`;
        XLSX.writeFile(wb, filename);
        
        showNotification('success', '导出成功！');
    } catch (error) {
        console.error('导出失败:', error);
        showNotification('error', '导出失败: ' + error.message);
    }
}

// 通知函数
function showNotification(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'warning' ? 'alert-warning' : 
                      type === 'error' ? 'alert-danger' : 'alert-info';
    
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-info-circle me-2"></i>
            <span>${message}</span>
            <button type="button" class="btn-close ms-auto" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (document.contains(notification)) {
            notification.remove();
        }
    }, 5000);
}

// ====== 调整模式功能类 ======

class LotAdjustmentManager {
    constructor() {
        this.originalData = [];
        this.currentData = [];
        this.groupedData = {};
        this.operationHistory = [];
        this.historyIndex = -1;
        this.maxHistory = 50;
        this.isModified = false;
        this.searchTerm = '';
        this.statusFilter = '';
        this.loadFilter = '';
    }

    // 更新数据
    updateData(data) {
        this.originalData = [...data];
        this.currentData = [...data];
        console.log(`📊 更新调整管理器数据: ${data.length} 条记录`);
    }

    // 按分选机分组数据
    groupDataByHandler(data = null) {
        const dataToGroup = data || this.getFilteredData();
        const grouped = {};
        dataToGroup.forEach(lot => {
            const handlerId = lot.HANDLER_ID || lot.handler_id || 'UNASSIGNED';
            if (!grouped[handlerId]) {
                grouped[handlerId] = {
                    handlerInfo: this.getHandlerInfo(handlerId),
                    lots: []
                };
            }
            grouped[handlerId].lots.push(lot);
        });

        // 按优先级排序
        Object.values(grouped).forEach(group => {
            group.lots.sort((a, b) => (a.PRIORITY || a.priority || 999) - (b.PRIORITY || b.priority || 999));
        });

        return grouped;
    }

    // 获取筛选后的数据
    getFilteredData() {
        let filtered = [...this.currentData];

        // 文本搜索
        if (this.searchTerm) {
            const term = this.searchTerm.trim().toLowerCase(); // 添加trim处理
            if (term) { // 只有在trim后不为空时才执行搜索
                filtered = filtered.filter(lot => {
                    const lotId = (lot.LOT_ID || lot.lot_id || '').toLowerCase();
                    const handlerId = (lot.HANDLER_ID || lot.handler_id || '').toLowerCase();
                    const device = (lot.DEVICE || lot.device || '').toLowerCase();
                    return lotId.includes(term) || handlerId.includes(term) || device.includes(term);
                });
            }
        }

        // 状态筛选
        if (this.statusFilter) {
            filtered = filtered.filter(lot => {
                if (this.statusFilter === 'failed') {
                    return lot.is_failed || lot.status === 'failed' || (lot.HANDLER_ID || lot.handler_id) === 'UNASSIGNED';
                } else {
                    // 排除失败批次，根据分选机状态筛选
                    if (lot.is_failed || lot.status === 'failed') return false;
                    const handlerId = lot.HANDLER_ID || lot.handler_id;
                    const handlerInfo = this.getHandlerInfo(handlerId);
                    return handlerInfo.status === this.statusFilter;
                }
            });
        }

        // 负载筛选
        if (this.loadFilter) {
            // 先按分选机分组来计算负载
            const tempGrouped = {};
            filtered.forEach(lot => {
                const handlerId = lot.HANDLER_ID || lot.handler_id || 'UNASSIGNED';
                if (!tempGrouped[handlerId]) {
                    tempGrouped[handlerId] = [];
                }
                tempGrouped[handlerId].push(lot);
            });

            // 筛选符合负载条件的分选机中的批次
            const filteredByLoad = [];
            Object.entries(tempGrouped).forEach(([handlerId, lots]) => {
                const load = this.calculateHandlerLoad(lots);
                let includeHandler = false;

                switch (this.loadFilter) {
                    case 'low':
                        includeHandler = load < 50;
                        break;
                    case 'medium':
                        includeHandler = load >= 50 && load < 75;
                        break;
                    case 'high':
                        includeHandler = load >= 75 && load <= 90;
                        break;
                    case 'critical':
                        includeHandler = load > 90;
                        break;
                }

                if (includeHandler) {
                    filteredByLoad.push(...lots);
                }
            });

            filtered = filteredByLoad;
        }

        return filtered;
    }

    // 应用搜索和筛选
    applyFilters(searchTerm = '', statusFilter = '', loadFilter = '') {
        this.searchTerm = searchTerm;
        this.statusFilter = statusFilter;
        this.loadFilter = loadFilter;
        
        // 重新分组并渲染
        groupedData = this.groupDataByHandler();
        renderHandlerGroups();
        updateAdjustmentStatistics();
        
        console.log(`🔍 应用筛选: 搜索="${searchTerm}", 状态="${statusFilter}", 负载="${loadFilter}"`);
    }

    // 获取分选机信息
    getHandlerInfo(handlerId) {
        return {
            id: handlerId,
            name: handlerId === 'UNASSIGNED' ? '未分配 (排产失败)' : handlerId,
            status: handlerId === 'UNASSIGNED' ? 'failed' : 'running',
            capacity: 8.0, // 小时
            currentLoad: 0.0,
            lotCount: 0
        };
    }

    // 计算分选机负载
    calculateHandlerLoad(lots) {
        if (!lots || lots.length === 0) return 0;
        
        const totalTime = lots.reduce((sum, lot) => {
            // 假设每1000个产品需要1小时处理时间
            const estimatedTime = (lot.GOOD_QTY || 0) / 1000;
            return sum + estimatedTime;
        }, 0);

        return Math.min(totalTime / 8.0 * 100, 100); // 转换为百分比，最大100%
    }

    // 添加操作记录
    addOperation(operation) {
        // 删除当前位置之后的操作
        this.operationHistory = this.operationHistory.slice(0, this.historyIndex + 1);
        
        // 生成唯一ID，避免重复
        const timestamp = Date.now();
        const uniqueId = `${timestamp}_${Math.random().toString(36).substr(2, 9)}`;
        
        const newOperation = {
            ...operation,
            timestamp: timestamp,
            id: uniqueId
        };
        
        // 检查是否是重复操作（基于type、lotId、描述的组合）
        const isDuplicate = this.operationHistory.some(existingOp => {
            const timeDiff = Math.abs(newOperation.timestamp - existingOp.timestamp);
            return timeDiff < 1000 && // 1秒内
                   existingOp.type === newOperation.type &&
                   existingOp.lotId === newOperation.lotId &&
                   existingOp.description === newOperation.description;
        });
        
        if (!isDuplicate) {
            // 添加新操作
            this.operationHistory.push(newOperation);

            // 限制历史记录数量
            if (this.operationHistory.length > this.maxHistory) {
                this.operationHistory = this.operationHistory.slice(-this.maxHistory);
            }

            this.historyIndex = this.operationHistory.length - 1;
            this.isModified = true;
            this.updateHistoryUI();
            console.log(`📝 添加操作记录: ${operation.description}`);
        } else {
            console.log(`⚠️ 跳过重复操作: ${operation.description}`);
        }
    }

    // 撤销操作
    undo() {
        if (this.canUndo()) {
            const operation = this.operationHistory[this.historyIndex];
            console.log(`🔄 撤销操作: ${operation.description}`);
            
            // 执行撤销逻辑
            this.executeUndo(operation);
            
            this.historyIndex--;
            this.isModified = true;
            this.updateHistoryUI();
            
            // 重新渲染界面
            groupedData = this.groupDataByHandler(currentData);
            renderHandlerGroups();
            updateAdjustmentStatistics();
            
            showNotification('info', `已撤销: ${operation.description}`);
        }
    }

    // 重做操作
    redo() {
        if (this.canRedo()) {
            this.historyIndex++;
            const operation = this.operationHistory[this.historyIndex];
            console.log(`🔄 重做操作: ${operation.description}`);
            
            // 执行重做逻辑
            this.executeRedo(operation);
            
            this.isModified = true;
            this.updateHistoryUI();
            
            // 重新渲染界面
            groupedData = this.groupDataByHandler(currentData);
            renderHandlerGroups();
            updateAdjustmentStatistics();
            
            showNotification('info', `已重做: ${operation.description}`);
        }
    }
    
    // 执行撤销逻辑
    executeUndo(operation) {
        switch (operation.type) {
            case 'drag_move':
            case 'move':
                this.undoMoveOperation(operation);
                break;
            case 'drag_reorder':
                this.undoReorderOperation(operation);
                break;
            case 'batch_move':
                this.undoBatchMoveOperation(operation);
                break;
            case 'batch_priority':
                this.undoBatchPriorityOperation(operation);
                break;
            case 'priority_change':
                this.undoPriorityChangeOperation(operation);
                break;
            default:
                console.warn(`未知的操作类型: ${operation.type}`);
        }
    }
    
    // 执行重做逻辑
    executeRedo(operation) {
        switch (operation.type) {
            case 'drag_move':
            case 'move':
                this.redoMoveOperation(operation);
                break;
            case 'drag_reorder':
                this.redoReorderOperation(operation);
                break;
            case 'batch_move':
                this.redoBatchMoveOperation(operation);
                break;
            case 'batch_priority':
                this.redoBatchPriorityOperation(operation);
                break;
            case 'priority_change':
                this.redoPriorityChangeOperation(operation);
                break;
            default:
                console.warn(`未知的操作类型: ${operation.type}`);
        }
    }
    
    // 撤销移动操作
    undoMoveOperation(operation) {
        const lotIndex = currentData.findIndex(lot => 
            (lot.LOT_ID || lot.lot_id) === operation.lotId
        );
        
        if (lotIndex !== -1) {
            // 恢复到原来的分选机
            currentData[lotIndex].HANDLER_ID = operation.oldHandler;
            currentData[lotIndex].handler_id = operation.oldHandler;
            
            // 如果是从正常分选机恢复到失败状态
            if (operation.oldHandler === 'UNASSIGNED') {
                currentData[lotIndex].is_failed = true;
                currentData[lotIndex].status = 'failed';
                currentData[lotIndex].WIP_STATE = 'FAILED';
            }
        }
    }
    
    // 重做移动操作
    redoMoveOperation(operation) {
        const lotIndex = currentData.findIndex(lot => 
            (lot.LOT_ID || lot.lot_id) === operation.lotId
        );
        
        if (lotIndex !== -1) {
            // 移动到新的分选机
            currentData[lotIndex].HANDLER_ID = operation.newHandler;
            currentData[lotIndex].handler_id = operation.newHandler;
            
            // 如果是从失败状态恢复到正常分选机
            if (operation.oldHandler === 'UNASSIGNED' && operation.newHandler !== 'UNASSIGNED') {
                currentData[lotIndex].is_failed = false;
                currentData[lotIndex].status = 'scheduled';
                currentData[lotIndex].WIP_STATE = 'SCHEDULED';
            }
        }
    }
    
    // 撤销重排序操作
    undoReorderOperation(operation) {
        // 对于重排序，我们需要存储更详细的状态信息
        // 这里可以实现基于时间戳的状态恢复
        console.log(`撤销重排序操作: ${operation.handlerId}`);
        
        // 简单实现：重新按原始优先级排序
        const handlerLots = currentData.filter(lot => 
            (lot.HANDLER_ID || lot.handler_id) === operation.handlerId
        );
        
        handlerLots.forEach((lot, index) => {
            lot.PRIORITY = lot.original_priority || index + 1;
            lot.priority = lot.original_priority || index + 1;
        });
    }
    
    // 重做重排序操作
    redoReorderOperation(operation) {
        console.log(`重做重排序操作: ${operation.handlerId}`);
        
        // 重新按当前位置计算优先级
        const handlerLots = currentData.filter(lot => 
            (lot.HANDLER_ID || lot.handler_id) === operation.handlerId
        );
        
        handlerLots.forEach((lot, index) => {
            lot.PRIORITY = index + 1;
            lot.priority = index + 1;
        });
    }
    
    // 撤销批量移动操作
    undoBatchMoveOperation(operation) {
        if (operation.lots && Array.isArray(operation.lots)) {
            operation.lots.forEach(lotInfo => {
                const lotIndex = currentData.findIndex(lot => 
                    (lot.LOT_ID || lot.lot_id) === lotInfo.lotId
                );
                
                if (lotIndex !== -1) {
                    currentData[lotIndex].HANDLER_ID = lotInfo.oldHandler;
                    currentData[lotIndex].handler_id = lotInfo.oldHandler;
                }
            });
        }
    }
    
    // 重做批量移动操作
    redoBatchMoveOperation(operation) {
        if (operation.lots && Array.isArray(operation.lots)) {
            operation.lots.forEach(lotInfo => {
                const lotIndex = currentData.findIndex(lot => 
                    (lot.LOT_ID || lot.lot_id) === lotInfo.lotId
                );
                
                if (lotIndex !== -1) {
                    currentData[lotIndex].HANDLER_ID = operation.targetHandler;
                    currentData[lotIndex].handler_id = operation.targetHandler;
                }
            });
        }
    }
    
    // 撤销批量优先级操作
    undoBatchPriorityOperation(operation) {
        if (operation.lots && Array.isArray(operation.lots)) {
            operation.lots.forEach(lotInfo => {
                const lotIndex = currentData.findIndex(lot => 
                    (lot.LOT_ID || lot.lot_id) === lotInfo.lotId
                );
                
                if (lotIndex !== -1) {
                    currentData[lotIndex].PRIORITY = lotInfo.oldPriority;
                    currentData[lotIndex].priority = lotInfo.oldPriority;
                }
            });
        }
    }
    
    // 重做批量优先级操作
    redoBatchPriorityOperation(operation) {
        if (operation.lots && Array.isArray(operation.lots)) {
            operation.lots.forEach(lotInfo => {
                const lotIndex = currentData.findIndex(lot => 
                    (lot.LOT_ID || lot.lot_id) === lotInfo.lotId
                );
                
                if (lotIndex !== -1) {
                    currentData[lotIndex].PRIORITY = lotInfo.newPriority;
                    currentData[lotIndex].priority = lotInfo.newPriority;
                }
            });
        }
    }
    
    // 撤销优先级变更操作
    undoPriorityChangeOperation(operation) {
        const lotIndex = currentData.findIndex(lot => 
            (lot.LOT_ID || lot.lot_id) === operation.lotId
        );
        
        if (lotIndex !== -1) {
            currentData[lotIndex].PRIORITY = operation.oldPriority;
            currentData[lotIndex].priority = operation.oldPriority;
        }
    }
    
    // 重做优先级变更操作
    redoPriorityChangeOperation(operation) {
        const lotIndex = currentData.findIndex(lot => 
            (lot.LOT_ID || lot.lot_id) === operation.lotId
        );
        
        if (lotIndex !== -1) {
            currentData[lotIndex].PRIORITY = operation.newPriority;
            currentData[lotIndex].priority = operation.newPriority;
        }
    }
    
    // 更新批次优先级（供拖拽管理器使用）
    updateLotPriority(lotId, handlerId, newPriority) {
        const lotIndex = currentData.findIndex(lot => 
            (lot.LOT_ID || lot.lot_id) === lotId
        );
        
        if (lotIndex !== -1) {
            const oldPriority = currentData[lotIndex].PRIORITY || currentData[lotIndex].priority;
            
            // 更新优先级
            currentData[lotIndex].PRIORITY = newPriority;
            currentData[lotIndex].priority = newPriority;
            
            // 记录操作历史（如果优先级有变化）
            if (oldPriority !== newPriority) {
                this.addOperation({
                    type: 'priority_change',
                    description: `调整批次 ${lotId} 优先级：${oldPriority} → ${newPriority}`,
                    lotId: lotId,
                    handlerId: handlerId,
                    oldPriority: oldPriority,
                    newPriority: newPriority
                });
            }
            
            this.isModified = true;
            return true;
        }
        
        return false;
    }
    
    // 清除操作历史
    clearHistory() {
        this.operationHistory = [];
        this.historyIndex = -1;
        this.updateHistoryUI();
        console.log('📝 操作历史已清除');
    }
    
    // 获取历史摘要
    getHistorySummary() {
        const summary = {
            totalOperations: this.operationHistory.length,
            currentPosition: this.historyIndex + 1,
            canUndo: this.canUndo(),
            canRedo: this.canRedo(),
            recentOperations: this.operationHistory.slice(-5).map(op => ({
                type: op.type,
                description: op.description,
                timestamp: op.timestamp
            }))
        };
        
        return summary;
    }

    canUndo() {
        return this.historyIndex >= 0;
    }

    canRedo() {
        return this.historyIndex < this.operationHistory.length - 1;
    }

    // 更新历史记录UI
    updateHistoryUI() {
        const historyContainer = document.getElementById('operationHistory');
        const undoBtn = document.getElementById('undoBtn');
        const redoBtn = document.getElementById('redoBtn');

        if (undoBtn) undoBtn.disabled = !this.canUndo();
        if (redoBtn) redoBtn.disabled = !this.canRedo();

        if (historyContainer) {
            if (this.operationHistory.length === 0) {
                historyContainer.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-clock"></i>
                        <small>暂无操作记录</small>
                    </div>
                `;
            } else {
                const recentOps = this.operationHistory.slice(-5); // 显示最近5条
                historyContainer.innerHTML = recentOps.map(op => `
                    <div class="operation-item">
                        <div>${op.description}</div>
                        <div class="operation-time">${new Date(op.timestamp).toLocaleTimeString()}</div>
                    </div>
                `).join('');
            }
        }
    }
}

// ====== 模式切换功能 ======

function switchToViewMode() {
    currentMode = 'view';
    console.log('🔄 切换到查看模式');
    
    // 隐藏调整相关按钮
    const saveBtn = document.getElementById('saveAdjustmentsBtn');
    const cancelBtn = document.getElementById('cancelAdjustmentsBtn');
    if (saveBtn) saveBtn.style.display = 'none';
    if (cancelBtn) cancelBtn.style.display = 'none';
    
    // 显示查看模式内容
    const viewContent = document.getElementById('viewModeContent');
    const adjustContent = document.getElementById('adjustModeContent');
    
    if (viewContent) viewContent.classList.remove('d-none');
    if (adjustContent) adjustContent.classList.add('d-none');
    
    // 重置拖拽功能初始化标志，以便下次切换到调整模式时可以重新初始化
    dragFeaturesInitialized = false;
    console.log('🔄 已重置拖拽功能初始化标志');
    
    showNotification('success', '已切换到查看模式');
}

function switchToFinalResultMode() {
    currentMode = 'final_result';
    console.log('🔄 切换到最终结果模式');
    
    // 隐藏调整相关按钮
    const saveBtn = document.getElementById('saveAdjustmentsBtn');
    const cancelBtn = document.getElementById('cancelAdjustmentsBtn');
    if (saveBtn) saveBtn.style.display = 'none';
    if (cancelBtn) cancelBtn.style.display = 'none';
    
    // 显示最终结果相关按钮
    const publishBtn = document.getElementById('publishResultBtn');
    const exportBtn = document.getElementById('exportFinalResultBtn');
    if (publishBtn) publishBtn.style.display = 'inline-block';
    if (exportBtn) exportBtn.style.display = 'inline-block';
    
    // 显示最终结果模式内容
    const viewContent = document.getElementById('viewModeContent');
    const adjustContent = document.getElementById('adjustModeContent');
    const finalResultContent = document.getElementById('finalResultModeContent');
    
    if (viewContent) viewContent.classList.add('d-none');
    if (adjustContent) adjustContent.classList.add('d-none');
    if (finalResultContent) finalResultContent.classList.remove('d-none');
    
    // 初始化筛选器选项
    initializeFinalResultFilters();
    
    // 加载最终结果数据
    loadFinalResultData();
    
    showNotification('success', '已切换到最终结果模式');
}

function switchToAdjustMode() {
    currentMode = 'adjust';
    console.log('🔄 切换到调整模式');
    
    // 显示调整相关按钮
    const saveBtn = document.getElementById('saveAdjustmentsBtn');
    const cancelBtn = document.getElementById('cancelAdjustmentsBtn');
    if (saveBtn) saveBtn.style.display = 'inline-block';
    if (cancelBtn) cancelBtn.style.display = 'inline-block';
    
    // 显示调整模式内容
    const viewContent = document.getElementById('viewModeContent');
    const adjustContent = document.getElementById('adjustModeContent');
    
    if (viewContent) viewContent.classList.add('d-none');
    if (adjustContent) adjustContent.classList.remove('d-none');
    
    // 检查是否已有数据，如果没有则重新加载
    if (!currentData || currentData.length === 0) {
        console.log('📊 没有数据，重新加载包含失败批次的数据');
    loadData(1, true).then(() => {
            initializeAdjustmentData();
        }).catch(error => {
            console.error('调整模式数据加载失败:', error);
            showNotification('error', '调整模式数据加载失败');
            // 切换回查看模式
            document.getElementById('viewModeBtn').checked = true;
                switchToViewMode();
        });
    } else {
        console.log('📊 使用现有数据初始化调整模式');
        initializeAdjustmentData();
    }
}

// 初始化调整数据的辅助函数
function initializeAdjustmentData() {
    try {
        console.log('🔧 初始化调整管理器数据');
        
        // 初始化调整数据
        if (adjustmentManager) {
            adjustmentManager.originalData = [...currentData];
            adjustmentManager.currentData = [...currentData];
            groupedData = adjustmentManager.groupDataByHandler(currentData);
            
            console.log(`📊 分组数据统计:`, {
                totalGroups: Object.keys(groupedData).length,
                totalLots: Object.values(groupedData).reduce((sum, group) => sum + group.lots.length, 0)
            });
            
            // 渲染分组界面
            renderHandlerGroups();
            updateAdjustmentStatistics();
            initializeAdjustmentFeatures();
        
        showNotification('success', '已切换到调整模式，包含失败批次数据');
        } else {
            throw new Error('调整管理器未初始化');
        }
    } catch (error) {
        console.error('❌ 初始化调整数据失败:', error);
        showNotification('error', `初始化调整数据失败: ${error.message}`);
    }
}

// ====== 分组界面渲染 ======

function renderHandlerGroups(forceFullRender = false) {
    const container = document.getElementById('handlerGroupsContainer');
    if (!container) return;
    
    if (Object.keys(groupedData).length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无分组数据</h5>
                <p class="text-muted">请先在查看模式中加载数据</p>
            </div>
        `;
        return;
    }
    
    // 如果数据量大且不是强制完整渲染，使用优化的渲染方式
    const totalLots = Object.values(groupedData).reduce((sum, group) => sum + group.lots.length, 0);
    if (totalLots > 150 && !forceFullRender) {
        renderHandlerGroupsOptimized();
        return;
    }
    
    // 原有的完整渲染逻辑
    let html = '';
    Object.entries(groupedData).forEach(([handlerId, group]) => {
        const load = adjustmentManager.calculateHandlerLoad(group.lots);
        const loadClass = load > 90 ? 'load-critical' : 
                         load > 75 ? 'load-high' : 
                         load > 50 ? 'load-medium' : 'load-low';
        
                html += `
            <div class="handler-group-card" data-handler-id="${handlerId}">
                <div class="handler-group-header">
                    <div class="handler-info">
                        <h6 class="mb-0"><i class="fas ${handlerId === 'UNASSIGNED' ? 'fa-clipboard-list' : 'fa-microchip'} me-2"></i>${group.handlerInfo.name}</h6>
                        <span class="handler-status status-${group.handlerInfo.status}">${getStatusText(group.handlerInfo.status)}</span>
                    </div>
                    <div class="load-info">
                        <small>${group.lots.length} 批次</small>
                    </div>
                </div>
                
                <div class="lot-list-sortable" data-handler-id="${handlerId}">
                    ${group.lots.map((lot, index) => renderLotCard(lot, index + 1)).join('')}
                </div>
                
                <div class="load-indicator">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <small>负载: ${load.toFixed(1)}%</small>
                        <small>${group.lots.reduce((sum, lot) => sum + (lot.GOOD_QTY || 0), 0).toLocaleString()} 件</small>
                    </div>
                    <div class="load-bar">
                        <div class="load-fill ${loadClass}" style="width: ${load}%"></div>
                    </div>
                </div>
        </div>
    `;
    });

    container.innerHTML = html;
    
    // DOM重建后自动重新初始化拖拽功能
    if (dragDropManager && currentMode === 'adjust') {
        setTimeout(() => {
            console.log('🔄 DOM重建后重新初始化拖拽功能');
            console.log(`📊 重新初始化前: 有 ${dragDropManager.sortableInstances.length} 个拖拽实例`);
            dragDropManager.initializeDragAndDrop();
            console.log(`📊 重新初始化后: 有 ${dragDropManager.sortableInstances.length} 个拖拽实例`);
        }, 100);
    }
}

// 优化的渲染方式，用于大数据量情况
function renderHandlerGroupsOptimized() {
    const container = document.getElementById('handlerGroupsContainer');
    
    // 分批渲染，避免一次性渲染太多DOM
    const handlerIds = Object.keys(groupedData);
    const batchSize = 5; // 每批渲染5个分选机组
    let currentBatch = 0;
    
    // 清空容器并显示加载提示
    container.innerHTML = `
        <div class="text-center py-3" id="renderingIndicator">
            <i class="fas fa-spinner fa-spin"></i> 正在渲染大量数据，请稍候...
        </div>
    `;
    
    function renderBatch() {
        const startIndex = currentBatch * batchSize;
        const endIndex = Math.min(startIndex + batchSize, handlerIds.length);
        
        if (startIndex === 0) {
            // 第一批时清空容器
            container.innerHTML = '';
        }
        
        let html = '';
        for (let i = startIndex; i < endIndex; i++) {
            const handlerId = handlerIds[i];
            const group = groupedData[handlerId];
            const load = adjustmentManager.calculateHandlerLoad(group.lots);
            const loadClass = load > 90 ? 'load-critical' : 
                             load > 75 ? 'load-high' : 
                             load > 50 ? 'load-medium' : 'load-low';
            
            // 只渲染前20个批次，大数据量时减少DOM元素
            const displayLots = group.lots.slice(0, 20);
            const hiddenCount = group.lots.length - displayLots.length;
            
            html += `
                <div class="handler-group-card" data-handler-id="${handlerId}">
                    <div class="handler-group-header">
                        <div class="handler-info">
                            <h6 class="mb-0"><i class="fas ${handlerId === 'UNASSIGNED' ? 'fa-clipboard-list' : 'fa-microchip'} me-2"></i>${group.handlerInfo.name}</h6>
                            <span class="handler-status status-${group.handlerInfo.status}">${getStatusText(group.handlerInfo.status)}</span>
                        </div>
                        <div class="load-info">
                            <small>${group.lots.length} 批次${hiddenCount > 0 ? ` (显示前${displayLots.length}个)` : ''}</small>
                        </div>
                    </div>
                    
                    <div class="lot-list-sortable" data-handler-id="${handlerId}">
                        ${displayLots.map((lot, index) => renderLotCard(lot, index + 1)).join('')}
                        ${hiddenCount > 0 ? `<div class="text-center py-2 text-muted"><small>还有 ${hiddenCount} 个批次未显示...</small></div>` : ''}
                    </div>
                    
                    <div class="load-indicator">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <small>负载: ${load.toFixed(1)}%</small>
                            <small>${group.lots.reduce((sum, lot) => sum + (lot.GOOD_QTY || 0), 0).toLocaleString()} 件</small>
                        </div>
                        <div class="load-bar">
                            <div class="load-fill ${loadClass}" style="width: ${load}%"></div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        // 添加到容器
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;
        while (tempDiv.firstChild) {
            container.appendChild(tempDiv.firstChild);
        }
        
        currentBatch++;
        
        // 继续渲染下一批或完成渲染
        if (currentBatch * batchSize < handlerIds.length) {
            setTimeout(renderBatch, 50); // 50ms间隔，避免阻塞UI
        } else {
            // 渲染完成，初始化拖拽功能
            if (dragDropManager && currentMode === 'adjust') {
                setTimeout(() => {
                    console.log('🔄 优化渲染完成，初始化拖拽功能');
                    dragDropManager.initializeDragAndDrop();
                }, 100);
            }
        }
    }
    
    // 开始渲染
    setTimeout(renderBatch, 100);
}

function renderLotCard(lot, priority) {
    const scoreClass = getScoreClass(lot.comprehensive_score);
    const lotId = lot.LOT_ID || lot.lot_id || 'N/A';
    const device = lot.DEVICE || lot.device || 'N/A';
    const quantity = lot.GOOD_QTY || lot.good_qty || lot.lot_qty || 0;
    const stage = lot.STAGE || lot.stage || 'N/A';
    const isFailedLot = lot.is_failed || lot.status === 'failed';
    
    const cardClass = isFailedLot ? 'lot-card failed-lot' : 'lot-card';
    const priorityBadge = isFailedLot ? 'FAIL' : priority;
    const priorityClass = isFailedLot ? 'failed-priority' : '';
    
    return `
        <div class="${cardClass}" data-lot-id="${lotId}" data-original-priority="${lot.PRIORITY || lot.priority || 999}" 
             draggable="true">
            <!-- 选择复选框 -->
            <div class="lot-checkbox position-absolute" style="top: 8px; right: 8px; z-index: 10;">
                <input type="checkbox" class="form-check-input lot-selector" data-lot-id="${lotId}">
            </div>
            
            <div class="lot-priority-badge ${priorityClass}">${priorityBadge}</div>
            <div class="lot-info">
                <div class="lot-details">
                    <div class="lot-id">${lotId}</div>
                    <div class="lot-device">${device}</div>
                    <div class="lot-quantity">${formatNumber(quantity)} 件</div>
                    ${isFailedLot ? `<div class="failure-reason text-danger"><small><i class="fas fa-exclamation-triangle"></i> ${lot.failure_reason || '排产失败'}</small></div>` : ''}
                </div>
                <div class="lot-actions">
                    ${isFailedLot ? 
                        `<span class="badge bg-danger">失败</span>` : 
                        `<span class="badge ${scoreClass}">${formatScore(lot.comprehensive_score)}</span>`
                    }
                    <small class="text-muted">${stage}</small>
                </div>
            </div>
        </div>
    `;
}

function getStatusText(status) {
    const statusMap = {
        'idle': '空闲',
        'running': '运行中',
        'maintenance': '维护中',
        'failed': '排产失败'
    };
    return statusMap[status] || status;
}

// ====== 拖拽功能 ======
// 拖拽功能已迁移到DragDropManager类中

// ====== 统计更新 ======

function updateAdjustmentStatistics() {
    const totalLots = Object.values(groupedData).reduce((sum, group) => sum + group.lots.length, 0);
    const totalHandlers = Object.keys(groupedData).length;
    const totalLoad = Object.values(groupedData).reduce((sum, group) => {
        return sum + adjustmentManager.calculateHandlerLoad(group.lots);
    }, 0);
    const avgLoad = totalHandlers > 0 ? totalLoad / totalHandlers : 0;
    const modifiedCount = adjustmentManager.operationHistory.length;

    const elements = {
        'adjustTotalLots': totalLots,
        'adjustTotalHandlers': totalHandlers,
        'adjustAverageLoad': avgLoad.toFixed(1) + '%',
        'modifiedLots': modifiedCount
    };

    Object.entries(elements).forEach(([id, value]) => {
        const el = document.getElementById(id);
        if (el) el.textContent = value;
    });
}

// ====== 调整模式功能初始化 ======

function initializeAdjustmentFeatures() {
    // 搜索功能
    const searchInput = document.getElementById('handlerSearchInput');
    const clearSearchBtn = document.getElementById('clearSearchBtn');
    const statusFilter = document.getElementById('handlerStatusFilter');
    const loadFilter = document.getElementById('loadFilter');
    const gridViewBtn = document.getElementById('gridViewBtn');
    const listViewBtn = document.getElementById('listViewBtn');

    if (searchInput) {
        // 实时搜索，防抖处理
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                applyCurrentFilters();
            }, 300);
        });
    }

    if (clearSearchBtn) {
        clearSearchBtn.addEventListener('click', function() {
            searchInput.value = '';
            applyCurrentFilters();
        });
    }

    if (statusFilter) {
        statusFilter.addEventListener('change', applyCurrentFilters);
    }

    if (loadFilter) {
        loadFilter.addEventListener('change', applyCurrentFilters);
    }

    if (gridViewBtn) {
        gridViewBtn.addEventListener('change', function() {
            if (this.checked) {
                document.getElementById('handlerGroupsContainer').className = 'handler-groups';
            }
        });
    }

    if (listViewBtn) {
        listViewBtn.addEventListener('change', function() {
            if (this.checked) {
                document.getElementById('handlerGroupsContainer').className = 'handler-groups list-view';
            }
        });
    }

    // 初始化拖拽功能
    initializeCrossMachineDrag();
    
    // 初始化批次选择功能
    initializeLotSelection();

    console.log('✅ 调整模式功能初始化完成');
}

// 初始化批次选择功能（已在initializeEventListeners中统一处理）
function initializeLotSelection() {
    console.log('✅ 批次选择功能初始化完成（统一事件处理）');
}

function applyCurrentFilters() {
    const searchTerm = document.getElementById('handlerSearchInput')?.value || '';
    const statusFilter = document.getElementById('handlerStatusFilter')?.value || '';
    const loadFilter = document.getElementById('loadFilter')?.value || '';

    if (adjustmentManager) {
        adjustmentManager.applyFilters(searchTerm, statusFilter, loadFilter);
    }
}

// ====== 批量操作功能 ======

// 全局选择的批次ID
let selectedLotIds = new Set();



// 全选批次 - 智能全选，只选择当前可见的批次
function selectAllLots() {
    let selectedCount = 0;
    let visibleCount = 0;
    
    // 只选择当前可见的批次复选框
    document.querySelectorAll('.lot-selector').forEach(checkbox => {
        const lotCard = checkbox.closest('.lot-card');
        
        // 检查批次卡片是否真正可见（未被筛选隐藏）
        if (lotCard && lotCard.offsetParent !== null) {
            visibleCount++;
            checkbox.checked = true;
            selectedCount++;
        }
    });
    
    updateLotSelection();
    
    // 显示选择结果反馈
    if (selectedCount > 0) {
        console.log(`✅ 全选完成: 选择了 ${selectedCount}/${visibleCount} 个可见批次`);
        
        // 检查是否在特定机台的筛选状态下
        const currentHandlerIds = new Set();
        selectedLotIds.forEach(lotId => {
            const lotData = currentData.find(lot => (lot.LOT_ID || lot.lot_id) === lotId);
            if (lotData) {
                const handlerId = lotData.HANDLER_ID || lotData.handler_id || 'UNASSIGNED';
                currentHandlerIds.add(handlerId);
            }
        });
        
        if (currentHandlerIds.size === 1) {
            const handlerId = Array.from(currentHandlerIds)[0];
            const handlerName = handlerId === 'UNASSIGNED' ? '失败批次' : handlerId;
            showNotification('success', `已选择 ${handlerName} 的全部 ${selectedCount} 个批次`);
        } else {
            showNotification('success', `已选择 ${selectedCount} 个批次，分布在 ${currentHandlerIds.size} 个分选机`);
        }
    } else {
        showNotification('warning', '没有找到可选择的批次');
    }
}

// 清除选择（优化DOM查询）
function clearLotSelection() {
    selectedLotIds.clear();
    
    // 一次性获取所有批次卡片，同时处理复选框和样式
    document.querySelectorAll('.lot-card').forEach(card => {
        const checkbox = card.querySelector('.lot-selector');
        if (checkbox) {
            checkbox.checked = false;
        }
        card.classList.remove('selected');
    });
    
    updateBatchOperationsUI();
}

// 更新批次选择状态（优化DOM查询）
function updateLotSelection() {
    selectedLotIds.clear();
    
    // 一次性获取所有批次卡片，避免重复查询
    const allLotCards = document.querySelectorAll('.lot-card');
    
    allLotCards.forEach(card => {
        const lotId = card.dataset.lotId;
        const checkbox = card.querySelector('.lot-selector');
        
        if (checkbox && checkbox.checked) {
            card.classList.add('selected');
            selectedLotIds.add(lotId);
        } else {
            card.classList.remove('selected');
        }
    });
    
    updateBatchOperationsUI();
}

// 更新批量操作界面
function updateBatchOperationsUI() {
    const batchOpsContainer = document.getElementById('adjustBatchOperations');
    const countElement = document.getElementById('selectedLotCount');
    const targetHandlerSelect = document.getElementById('targetHandlerSelect');
    
    if (countElement) {
        countElement.textContent = selectedLotIds.size;
    }
    
    if (batchOpsContainer) {
        if (selectedLotIds.size > 0) {
            batchOpsContainer.style.display = 'block';
            
            // 更新目标分选机选项 - 智能排除当前机台
            if (targetHandlerSelect && groupedData) {
                targetHandlerSelect.innerHTML = '<option value="">选择目标分选机...</option>';
                
                // 获取已选批次当前所在的分选机集合
                const currentHandlerIds = new Set();
                selectedLotIds.forEach(lotId => {
                    const lotData = currentData.find(lot => (lot.LOT_ID || lot.lot_id) === lotId);
                    if (lotData) {
                        const handlerId = lotData.HANDLER_ID || lotData.handler_id || 'UNASSIGNED';
                        currentHandlerIds.add(handlerId);
                    }
                });
                
                console.log(`📋 已选批次分布在机台:`, Array.from(currentHandlerIds));
                
                // 生成目标分选机选项，排除当前机台（但允许移动到UNASSIGNED）
                let availableTargets = 0;
                Object.keys(groupedData).forEach(handlerId => {
                    if (handlerId !== 'UNASSIGNED') {
                        // 只有当不是所有选中批次都在此机台时，才显示此机台作为目标
                        const isCurrentHandler = currentHandlerIds.has(handlerId) && currentHandlerIds.size === 1;
                        
                        if (!isCurrentHandler) {
                            const option = document.createElement('option');
                            option.value = handlerId;
                            option.textContent = `${handlerId} (${groupedData[handlerId].lots.length} 批次)`;
                            targetHandlerSelect.appendChild(option);
                            availableTargets++;
                        }
                    }
                });
                
                // 始终添加UNASSIGNED选项（用于标记失败）
                const unassignedOption = document.createElement('option');
                unassignedOption.value = 'UNASSIGNED';
                unassignedOption.textContent = '未分配 (失败批次)';
                targetHandlerSelect.appendChild(unassignedOption);
                availableTargets++;
                
                // 如果没有可用的目标机台，显示提示
                if (availableTargets === 1) { // 只有UNASSIGNED选项
                    const noTargetOption = document.createElement('option');
                    noTargetOption.value = '';
                    noTargetOption.textContent = '无其他可用分选机';
                    noTargetOption.disabled = true;
                    targetHandlerSelect.insertBefore(noTargetOption, unassignedOption);
                }
                
                console.log(`🎯 可用目标分选机数量: ${availableTargets - 1} 个正常机台 + 1 个失败选项`);
            }
        } else {
            batchOpsContainer.style.display = 'none';
        }
    }
}

// 批量移动到指定分选机
function batchMoveToHandler() {
    const targetHandlerId = document.getElementById('targetHandlerSelect').value;
    
    if (!targetHandlerId) {
        showNotification('warning', '请选择目标分选机');
        return;
    }
    
    if (selectedLotIds.size === 0) {
        showNotification('warning', '请先选择要移动的批次');
        return;
    }
    
    // 检查是否尝试移动到相同机台
    const selectedLots = Array.from(selectedLotIds);
    const sameHandlerLots = [];
    const validMoves = [];
    
    selectedLots.forEach(lotId => {
        const lotData = currentData.find(lot => (lot.LOT_ID || lot.lot_id) === lotId);
        if (lotData) {
            const currentHandlerId = lotData.HANDLER_ID || lotData.handler_id || 'UNASSIGNED';
            if (currentHandlerId === targetHandlerId) {
                sameHandlerLots.push(lotId);
            } else {
                validMoves.push(lotId);
            }
        }
    });
    
    // 如果有批次已经在目标机台，给出提示
    if (sameHandlerLots.length > 0) {
        const proceed = confirm(
            `警告：有 ${sameHandlerLots.length} 个批次已经在目标分选机 ${targetHandlerId} 中：\n` +
            `${sameHandlerLots.slice(0, 3).join(', ')}${sameHandlerLots.length > 3 ? '...' : ''}\n\n` +
            `这些批次将被跳过。只移动其他 ${validMoves.length} 个批次，确定继续吗？`
        );
        
        if (!proceed) {
            return;
        }
    }
    
    if (validMoves.length === 0) {
        showNotification('info', `所有选中的批次都已经在 ${targetHandlerId} 中，无需移动`);
        return;
    }
    
    const finalConfirmMessage = validMoves.length === selectedLots.length 
        ? `确定要将选中的 ${validMoves.length} 个批次移动到 ${targetHandlerId} 吗？`
        : `确定要将 ${validMoves.length} 个有效批次移动到 ${targetHandlerId} 吗？（跳过 ${sameHandlerLots.length} 个已在目标机台的批次）`;
    
    if (confirm(finalConfirmMessage)) {
        // 实现批量移动逻辑 - 只处理有效移动
        const movedLots = [];
        
        validMoves.forEach(lotId => {
            // 在当前数据中找到这个批次
            const lotIndex = currentData.findIndex(lot => (lot.LOT_ID || lot.lot_id) === lotId);
            if (lotIndex !== -1) {
                const oldHandlerId = currentData[lotIndex].HANDLER_ID || currentData[lotIndex].handler_id;
                
                // 更新分选机ID
                currentData[lotIndex].HANDLER_ID = targetHandlerId;
                currentData[lotIndex].handler_id = targetHandlerId;
                
                // 如果是失败批次移动到正常分选机，更新状态
                if (currentData[lotIndex].is_failed && targetHandlerId !== 'UNASSIGNED') {
                    currentData[lotIndex].is_failed = false;
                    currentData[lotIndex].status = 'scheduled';
                    currentData[lotIndex].WIP_STATE = 'SCHEDULED';
                }
                
                movedLots.push({
                    ...currentData[lotIndex],
                    _oldHandlerId: oldHandlerId
                });
            }
        });
        
        if (movedLots.length > 0) {
            // 记录操作历史
            adjustmentManager.addOperation({
                type: 'batch_move',
                description: `批量移动 ${movedLots.length} 个批次到 ${targetHandlerId}`,
                lots: movedLots,
                targetHandler: targetHandlerId
            });
            
            // 重新分组和渲染
            groupedData = adjustmentManager.groupDataByHandler(currentData);
            renderHandlerGroups();
            updateAdjustmentStatistics();
            
            // 清除选择
            clearLotSelection();
            
            showNotification('success', `✅ 批量移动完成: ${movedLots.length} 个批次 → ${targetHandlerId}`);
        }
    }
}

// 批量调整优先级
function batchChangePriority() {
    if (selectedLotIds.size === 0) {
        showNotification('warning', '请先选择要调整的批次');
        return;
    }
    
    const newPriority = prompt('请输入新的优先级 (1-999，数字越小优先级越高):');
    
    if (newPriority === null) return;
    
    const priority = parseInt(newPriority);
    if (isNaN(priority) || priority < 1 || priority > 999) {
        showNotification('error', '优先级必须是1-999之间的数字');
        return;
    }
    
    const selectedLots = Array.from(selectedLotIds);
    const updatedLots = [];
    
    selectedLots.forEach(lotId => {
        const lotIndex = currentData.findIndex(lot => (lot.LOT_ID || lot.lot_id) === lotId);
        if (lotIndex !== -1) {
            const oldPriority = currentData[lotIndex].PRIORITY || currentData[lotIndex].priority || 999;
            currentData[lotIndex].PRIORITY = priority;
            currentData[lotIndex].priority = priority;
            updatedLots.push({
                lotId: lotId,
                oldPriority: oldPriority,
                newPriority: priority
            });
        }
    });
    
    if (updatedLots.length > 0) {
        // 记录操作历史
        adjustmentManager.addOperation({
            type: 'batch_priority',
            description: `批量调整 ${updatedLots.length} 个批次优先级为 ${priority}`,
            lots: updatedLots
        });
        
        // 重新分组和渲染
        groupedData = adjustmentManager.groupDataByHandler(currentData);
        renderHandlerGroups();
        updateAdjustmentStatistics();
        
        showNotification('success', `✅ 批量优先级调整完成: ${updatedLots.length} 个批次`);
    }
}

// 批量删除批次
function batchDeleteLots() {
    if (selectedLotIds.size === 0) {
        showNotification('warning', '请先选择要删除的批次');
        return;
    }
    
    const selectedLots = Array.from(selectedLotIds);
    
    // 增强确认对话框
    const confirmMessage = `⚠️ 危险操作确认\n\n您即将删除选中的 ${selectedLots.length} 个批次：\n${selectedLots.slice(0, 3).join('\n')}${selectedLots.length > 3 ? '\n...' : ''}\n\n删除后数据将无法恢复，确定要继续吗？`;
    
    if (confirm(confirmMessage)) {
        const deletedLots = [];
        
        // 从当前数据中移除选中的批次
        selectedLots.forEach(lotId => {
            const lotIndex = currentData.findIndex(lot => (lot.LOT_ID || lot.lot_id) === lotId);
            if (lotIndex !== -1) {
                deletedLots.push({...currentData[lotIndex]});
                currentData.splice(lotIndex, 1);
            }
        });
        
        if (deletedLots.length > 0) {
            // 记录操作历史
            adjustmentManager.addOperation({
                type: 'batch_delete',
                description: `批量删除 ${deletedLots.length} 个批次`,
                lots: deletedLots,
                reversible: true // 标记为可撤销
            });
            
            // 重新分组和渲染
            groupedData = adjustmentManager.groupDataByHandler(currentData);
            renderHandlerGroups();
            updateAdjustmentStatistics();
            
            // 清除选择
            clearLotSelection();
            
            showNotification('success', `✅ 批量删除完成: ${deletedLots.length} 个批次`);
            console.log(`🗑️ 已删除批次:`, deletedLots.map(lot => lot.LOT_ID || lot.lot_id));
        }
    }
}

// 批量标记为失败
function batchResetToFailed() {
    if (selectedLotIds.size === 0) {
        showNotification('warning', '请先选择要标记为失败的批次');
        return;
    }
    
    const selectedLots = Array.from(selectedLotIds);
    const reason = prompt('请输入失败原因 (可选):') || '手动标记失败';
    
    if (confirm(`确定要将选中的 ${selectedLots.length} 个批次标记为失败吗？`)) {
        const failedLots = [];
        
        selectedLots.forEach(lotId => {
            const lotIndex = currentData.findIndex(lot => (lot.LOT_ID || lot.lot_id) === lotId);
            if (lotIndex !== -1) {
                const oldState = {
                    handler_id: currentData[lotIndex].HANDLER_ID || currentData[lotIndex].handler_id,
                    is_failed: currentData[lotIndex].is_failed,
                    status: currentData[lotIndex].status,
                    wip_state: currentData[lotIndex].WIP_STATE
                };
                
                // 标记为失败
                currentData[lotIndex].HANDLER_ID = 'UNASSIGNED';
                currentData[lotIndex].handler_id = 'UNASSIGNED';
                currentData[lotIndex].is_failed = true;
                currentData[lotIndex].status = 'failed';
                currentData[lotIndex].WIP_STATE = 'FAILED';
                currentData[lotIndex].failure_reason = reason;
                currentData[lotIndex].failure_time = new Date().toISOString();
                
                failedLots.push({
                    lotId: lotId,
                    oldState: oldState,
                    reason: reason
                });
            }
        });
        
        if (failedLots.length > 0) {
            // 记录操作历史
            adjustmentManager.addOperation({
                type: 'batch_mark_failed',
                description: `批量标记失败 ${failedLots.length} 个批次: ${reason}`,
                lots: failedLots,
                reversible: true // 标记为可撤销
            });
            
            // 重新分组和渲染
            groupedData = adjustmentManager.groupDataByHandler(currentData);
            renderHandlerGroups();
            updateAdjustmentStatistics();
            
            // 清除选择
            clearLotSelection();
            
            showNotification('warning', `⚠️ 批量标记失败完成: ${failedLots.length} 个批次`);
        }
    }
}

// 增强版全选功能（支持按分选机全选）
function selectAllLotsInHandler(handlerId) {
    // 清除之前的选择
    clearLotSelection();
    
    // 选择指定分选机的所有批次
    const handlerCard = document.querySelector(`[data-handler-id="${handlerId}"]`);
    if (handlerCard) {
        const checkboxes = handlerCard.querySelectorAll('.lot-selector');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
        updateLotSelection();
        
        showNotification('info', `已选中分选机 ${handlerId} 的 ${checkboxes.length} 个批次`);
    }
}

// 按条件筛选选择
function selectLotsByCondition(condition) {
    clearLotSelection();
    
    let count = 0;
    document.querySelectorAll('.lot-card').forEach(card => {
        const lotId = card.dataset.lotId;
        const lotData = currentData.find(lot => (lot.LOT_ID || lot.lot_id) === lotId);
        
        if (lotData && matchesCondition(lotData, condition)) {
            const checkbox = card.querySelector('.lot-selector');
            if (checkbox) {
                checkbox.checked = true;
                count++;
            }
        }
    });
    
    updateLotSelection();
    showNotification('info', `按条件筛选选择了 ${count} 个批次`);
}

// 条件匹配函数
function matchesCondition(lotData, condition) {
    switch (condition) {
        case 'failed':
            return lotData.is_failed || lotData.status === 'failed';
        case 'high_priority':
            return (lotData.PRIORITY || lotData.priority || 999) <= 10;
        case 'low_score':
            return (lotData.comprehensive_score || 0) < 60;
        case 'large_quantity':
            return (lotData.GOOD_QTY || lotData.good_qty || 0) > 50000;
        default:
            return false;
    }
}

// ====== 拖拽功能增强 ======

// 跨分选机拖拽支持（防止重复初始化）
function initializeCrossMachineDrag() {
    // 防止重复绑定事件
    if (window.crossMachineDragInitialized) {
        console.log('⚠️ 跨机拖拽事件已初始化，跳过重复初始化');
        return;
    }
    window.crossMachineDragInitialized = true;
    
    document.addEventListener('dragstart', function(e) {
        if (e.target.classList.contains('lot-card')) {
            e.dataTransfer.setData('text/plain', e.target.dataset.lotId);
            e.target.classList.add('dragging');
        }
    });
    
    document.addEventListener('dragend', function(e) {
        if (e.target.classList.contains('lot-card')) {
            e.target.classList.remove('dragging');
            document.querySelectorAll('.drag-over').forEach(el => {
                el.classList.remove('drag-over');
            });
        }
    });
    
    // 为分选机组添加拖拽目标支持
    document.addEventListener('dragover', function(e) {
        const handlerCard = e.target.closest('.handler-group-card');
        if (handlerCard) {
            e.preventDefault();
            handlerCard.classList.add('drag-over');
        }
    });
    
    document.addEventListener('dragleave', function(e) {
        const handlerCard = e.target.closest('.handler-group-card');
        if (handlerCard && !handlerCard.contains(e.relatedTarget)) {
            handlerCard.classList.remove('drag-over');
        }
    });
    
    document.addEventListener('drop', function(e) {
        const handlerCard = e.target.closest('.handler-group-card');
        if (handlerCard) {
            e.preventDefault();
            
            const lotId = e.dataTransfer.getData('text/plain');
            const targetHandlerId = handlerCard.dataset.handlerId;
            
            if (lotId && targetHandlerId) {
                moveLotToHandler(lotId, targetHandlerId);
            }
            
            handlerCard.classList.remove('drag-over');
        }
    });
    
    console.log('✅ 跨分选机拖拽功能初始化完成');
}

// 移动批次到指定分选机
function moveLotToHandler(lotId, targetHandlerId) {
    const lotIndex = currentData.findIndex(lot => (lot.LOT_ID || lot.lot_id) === lotId);
    
    if (lotIndex !== -1) {
        const oldHandlerId = currentData[lotIndex].HANDLER_ID || currentData[lotIndex].handler_id;
        
        if (oldHandlerId === targetHandlerId) {
            return; // 没有改变
        }
        
        // 更新分选机ID
        currentData[lotIndex].HANDLER_ID = targetHandlerId;
        currentData[lotIndex].handler_id = targetHandlerId;
        
        // 如果是失败批次移动到正常分选机，更新状态
        if (currentData[lotIndex].is_failed && targetHandlerId !== 'UNASSIGNED') {
            currentData[lotIndex].is_failed = false;
            currentData[lotIndex].status = 'scheduled';
            currentData[lotIndex].WIP_STATE = 'SCHEDULED';
        }
        
        // 记录操作历史
        adjustmentManager.addOperation({
            type: 'move',
            description: `移动批次 ${lotId} 从 ${oldHandlerId} 到 ${targetHandlerId}`,
            lotId: lotId,
            oldHandler: oldHandlerId,
            newHandler: targetHandlerId
        });
        
        // 重新分组和渲染
        groupedData = adjustmentManager.groupDataByHandler(currentData);
        renderHandlerGroups();
        updateAdjustmentStatistics();
        
        showNotification('success', `批次 ${lotId} 已移动到 ${targetHandlerId}`);
    }
}

// ====== 保存和取消功能 ======

async function saveAdjustments() {
    if (!adjustmentManager.isModified) {
        showNotification('info', '没有需要保存的修改');
        return;
    }

    // 保存前执行完整验证
    if (validationManager) {
        showNotification('info', '正在验证调整合理性...');
        
        const validationResult = await validationManager.performComprehensiveValidation();
        
        if (!validationResult.valid) {
            const proceed = confirm(`验证发现问题：\n${validationResult.errors.join('\n')}\n\n是否仍要继续保存？`);
            if (!proceed) {
                showNotification('info', '保存已取消');
                return;
            }
        } else if (validationResult.warnings.length > 0) {
            const proceed = confirm(`验证发现警告：\n${validationResult.warnings.join('\n')}\n\n是否继续保存？`);
            if (!proceed) {
                showNotification('info', '保存已取消');
                return;
            }
        }
    }

    if (confirm('确定要保存所有调整吗？保存后将无法撤销。')) {
        try {
            const success = await saveFinalSchedulingResult();
            if (success) {
                console.log('✅ 调整保存成功');
        // 重置修改状态
        adjustmentManager.isModified = false;
        adjustmentManager.operationHistory = [];
        adjustmentManager.historyIndex = -1;
        adjustmentManager.updateHistoryUI();
        
                showNotification('success', '调整结果已保存到最终排产结果表');
            }
        } catch (error) {
            console.error('❌ 保存调整失败:', error);
            showNotification('error', `保存调整失败: ${error.message}`);
        }
    }
}

// 保存最终排产结果到final_scheduling_result表
async function saveFinalSchedulingResult() {
    try {
        console.log('📤 开始保存最终排产结果...');
        
        // 获取当前会话信息
        const urlParams = new URLSearchParams(window.location.search);
        const sessionId = urlParams.get('session') || `adjust_${Date.now()}`;
        const sessionName = `调整会话 ${new Date().toLocaleString()}`;
        
        // 收集所有调整数据
        const adjustments = [];
        
        Object.entries(groupedData).forEach(([handlerId, group]) => {
            group.lots.forEach((lot, index) => {
                const lotId = lot.LOT_ID || lot.lot_id;
                
                // 验证LOT_ID是否有效
                if (!lotId || lotId.trim() === '') {
                    console.warn('⚠️ 发现无效的LOT_ID:', lot);
                    return;
                }
                
                const newPriority = index + 1;
                const currentHandlerId = lot.HANDLER_ID || lot.handler_id;
                const originalPriority = lot.original_priority || lot.PRIORITY || lot.priority;
                const originalHandlerId = lot.original_handler_id || currentHandlerId;
                
                // 确定调整类型
                let adjustmentType = 'none';
                if (originalPriority !== newPriority && originalHandlerId !== handlerId) {
                    adjustmentType = 'both';
                } else if (originalPriority !== newPriority) {
                    adjustmentType = 'priority_only';
                } else if (originalHandlerId !== handlerId) {
                    adjustmentType = 'handler_only';
                } else if (lot.is_failed === true || lot.status === 'failed') {
                    adjustmentType = 'recovery';
                }
                
                // 确定数据来源类型
                let sourceType = 'success';
                if (lot.is_failed === true || lot.status === 'failed') {
                    sourceType = 'failed';
                } else if (lot.is_manual === true) {
                    sourceType = 'manual';
                }
                
                const adjustmentData = {
                    lot_id: lotId,
                    original_priority: originalPriority,
                    original_handler_id: currentHandlerId,  // 使用当前分选机作为原始值
                    final_priority: newPriority,
                    final_handler_id: handlerId,  // 使用目标分选机作为最终值
                    adjustment_type: adjustmentType,
                    adjustment_reason: lot.adjustment_reason || (adjustmentType !== 'none' ? '手动调整' : null),
                    source_type: sourceType,
                    failure_reason: lot.failure_reason || null,
                    lot_data: {
                        LOT_ID: lotId,
                        LOT_TYPE: lot.LOT_TYPE || lot.lot_type,
                        GOOD_QTY: lot.GOOD_QTY || lot.good_qty || 0,
                        PROD_ID: lot.PROD_ID || lot.prod_id,
                        DEVICE: lot.DEVICE || lot.device,
                        CHIP_ID: lot.CHIP_ID || lot.chip_id,
                        PKG_PN: lot.PKG_PN || lot.pkg_pn,
                        PO_ID: lot.PO_ID || lot.po_id,
                        STAGE: lot.STAGE || lot.stage,
                        WIP_STATE: lot.WIP_STATE || lot.wip_state || 'SCHEDULED',
                        PROC_STATE: lot.PROC_STATE || lot.proc_state || 'READY',
                        HOLD_STATE: lot.HOLD_STATE || lot.hold_state,
                        FLOW_ID: lot.FLOW_ID || lot.flow_id,
                        FLOW_VER: lot.FLOW_VER || lot.flow_ver,
                        RELEASE_TIME: lot.RELEASE_TIME || lot.release_time,
                        FAC_ID: lot.FAC_ID || lot.fac_id,
                        CREATE_TIME: lot.CREATE_TIME || lot.create_time,
                        comprehensive_score: lot.comprehensive_score,
                        processing_time: lot.processing_time,
                        changeover_time: lot.changeover_time,
                        match_type: lot.match_type,
                        algorithm_version: lot.algorithm_version
                    }
                };
                
                adjustments.push(adjustmentData);
            });
        });
        
        if (adjustments.length === 0) {
            showNotification('warning', '没有找到需要保存的调整数据');
            return false;
        }
        
        console.log(`📊 准备保存 ${adjustments.length} 条调整记录`);
        
        // 发送保存请求
        const response = await fetch('/api/v2/production/final-scheduling/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                session_id: sessionId,
                session_name: sessionName,
                adjustments: adjustments
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        console.log('📥 保存响应:', result);
        
        if (result.success) {
            const data = result.data;
            console.log(`✅ 保存成功: ${data.saved_count}/${data.total_lots} 条记录`);
            
            // 更新URL参数，确保刷新后能看到保存的结果
            const newUrl = new URL(window.location);
            newUrl.searchParams.set('session', sessionId);
            newUrl.searchParams.set('mode', 'final_result');
            window.history.pushState({}, '', newUrl);
            
            return true;
        } else {
            throw new Error(result.message || '保存失败');
        }
        
    } catch (error) {
        console.error('❌ 保存最终排产结果失败:', error);
        throw error;
    }
}

// 初始化最终结果筛选器选项
async function initializeFinalResultFilters() {
    try {
        console.log('🔧 初始化最终结果筛选器选项...');
        
        // 获取可用的会话列表
        const sessionResponse = await fetch('/api/v2/production/final-scheduling/sessions', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        if (sessionResponse.ok) {
            const sessionResult = await sessionResponse.json();
            const sessionFilter = document.getElementById('finalResultSessionFilter');
            if (sessionFilter && sessionResult.success) {
                sessionFilter.innerHTML = '<option value="">选择调整会话...</option>';
                sessionResult.data.forEach(session => {
                    const option = document.createElement('option');
                    option.value = session.session_id;
                    option.textContent = `${session.session_name} (${session.total_lots}批次)`;
                    sessionFilter.appendChild(option);
                });
            }
        }
        
        // 初始化分选机选项（基于当前数据）
        const handlerFilter = document.getElementById('finalResultHandlerFilter');
        if (handlerFilter) {
            handlerFilter.innerHTML = '<option value="">所有分选机</option>';
            // 如果有当前数据，从中提取分选机选项
            if (currentData && currentData.length > 0) {
                const handlers = [...new Set(currentData.map(item => item.HANDLER_ID).filter(id => id))];
                handlers.sort().forEach(handlerId => {
                    const option = document.createElement('option');
                    option.value = handlerId;
                    option.textContent = handlerId;
                    handlerFilter.appendChild(option);
                });
            }
        }
        
        console.log('✅ 最终结果筛选器选项初始化完成');
    } catch (error) {
        console.error('❌ 初始化最终结果筛选器失败:', error);
    }
}

// 加载最终结果数据
async function loadFinalResultData(page = 1) {
    try {
        console.log('📊 开始加载最终结果数据...');
        
        // 获取筛选条件
        const sessionId = document.getElementById('finalResultSessionFilter')?.value || '';
        const handlerId = document.getElementById('finalResultHandlerFilter')?.value || '';
        const searchTerm = document.getElementById('finalResultSearchInput')?.value || '';
        
        // 构建请求参数
        const params = new URLSearchParams({
            page: page.toString(),
            size: '50'
        });
        
        if (sessionId) params.append('session_id', sessionId);
        if (handlerId) params.append('handler_id', handlerId);
        if (searchTerm) params.append('search', searchTerm);
        
        const response = await fetch(`/api/v2/production/final-scheduling/data?${params.toString()}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        console.log('📥 最终结果数据响应:', result);
        
        if (result.success) {
            const data = result.data || [];
            const pagination = result.pagination || {};
            const sessionInfo = result.session_info || {};
            
            // 更新表格数据
            renderFinalResultTable(data);
            
            // 更新分页信息
            updateFinalResultPagination(pagination);
            
            // 更新会话信息
            updateFinalResultSessionInfo(sessionInfo);
            
            console.log(`✅ 最终结果数据加载成功: ${data.length} 条记录`);
            
        } else {
            throw new Error(result.message || '加载最终结果数据失败');
        }
        
    } catch (error) {
        console.error('❌ 加载最终结果数据失败:', error);
        showNotification('error', `加载最终结果数据失败: ${error.message}`);
        
        // 显示错误状态
        const tableBody = document.getElementById('finalResultTableBody');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="12" class="text-center py-5">
                        <div class="text-danger">
                            <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                            <p>加载最终结果数据失败</p>
                            <p class="small text-muted">${error.message}</p>
                    </div>
                    </td>
                </tr>
            `;
        }
    }
}

// 渲染最终结果表格
function renderFinalResultTable(data) {
    const tableBody = document.getElementById('finalResultTableBody');
    if (!tableBody) return;
    
    if (!data || data.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="12" class="text-center py-5">
                    <div class="text-muted">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p>暂无最终排产调整结果</p>
                        <p class="small">请先在调整模式中完成调整并保存</p>
                        </div>
                </td>
            </tr>
        `;
                return;
            }
            
    const rows = data.map((item, index) => {
        const sourceTypeClass = {
            'success': 'success',
            'failed': 'danger',
            'manual': 'warning'
        }[item.SOURCE_TYPE] || 'secondary';
        
        const adjustmentTypeClass = {
            'both': 'primary',
            'priority_only': 'info',
            'handler_only': 'warning',
            'recovery': 'success',
            'none': 'secondary'
        }[item.ADJUSTMENT_TYPE] || 'secondary';
        
        return `
            <tr data-id="${item.ID || index}">
                <td>
                    <input type="checkbox" class="row-checkbox" value="${item.ID || index}">
                </td>
                <td>
                    <span class="lot-id-column">${item.LOT_ID || 'N/A'}</span>
                </td>
                <td>
                    <strong>${item.DEVICE || 'N/A'}</strong>
                    ${item.PROD_ID ? `<br><small class="text-muted">${item.PROD_ID}</small>` : ''}
                </td>
                <td>
                    <span class="quantity-column">${formatNumber(item.GOOD_QTY || 0)}</span>
                </td>
                <td>
                    <span class="handler-info">${item.ORIGINAL_HANDLER_ID || 'N/A'}</span>
                </td>
                <td>
                    <span class="badge badge-success ${getPriorityClass(item.ORIGINAL_PRIORITY)}">${item.ORIGINAL_PRIORITY || 'N/A'}</span>
                </td>
                <td>
                    <span class="badge bg-${sourceTypeClass}">${item.SOURCE_TYPE || 'N/A'}</span>
                </td>
                <td>
                    <span class="badge bg-${adjustmentTypeClass}">${item.ADJUSTMENT_TYPE || 'N/A'}</span>
                </td>
                <td>
                    ${item.FINAL_PRIORITY ? `<span class="badge badge-secondary ${getPriorityClass(item.FINAL_PRIORITY)}">${item.FINAL_PRIORITY}</span>` : '-'}
                </td>
                <td>
                    ${item.FINAL_HANDLER_ID ? `<span class="handler-primary">${item.FINAL_HANDLER_ID}</span>` : '-'}
                </td>
                <td>
                    <small>${item.ADJUSTED_BY || '-'}</small>
                </td>
                <td>
                    <span class="datetime-value">${formatDateTime(item.ADJUSTED_AT)}</span>
                </td>
            </tr>
        `;
    }).join('');
    
    tableBody.innerHTML = rows;
}

// 更新最终结果分页信息
function updateFinalResultPagination(pagination) {
    const { page = 1, size = 50, total = 0, pages = 0 } = pagination;
    
    // 更新记录数显示
    const startRecord = (page - 1) * size + 1;
    const endRecord = Math.min(page * size, total);
    
    document.getElementById('finalResultStartRecord').textContent = total > 0 ? startRecord : 0;
    document.getElementById('finalResultEndRecord').textContent = endRecord;
    document.getElementById('finalResultTotalCount').textContent = total;
    
    // 更新分页按钮
    const paginationEl = document.getElementById('finalResultPagination');
    if (paginationEl) {
        paginationEl.innerHTML = generatePaginationButtons(page, pages, 'loadFinalResultData');
    }
}

// 生成分页按钮
function generatePaginationButtons(currentPage, totalPages, clickFunction) {
    if (totalPages <= 1) {
        return '';
    }
    
    let html = '';
    
    // 上一页
    html += `
        <li class="page-item ${currentPage <= 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="${clickFunction}(${currentPage - 1}); return false;">上一页</a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    if (startPage > 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="${clickFunction}(1); return false;">1</a></li>`;
        if (startPage > 2) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        html += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="${clickFunction}(${i}); return false;">${i}</a>
            </li>
        `;
    }
    
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        html += `<li class="page-item"><a class="page-link" href="#" onclick="${clickFunction}(${totalPages}); return false;">${totalPages}</a></li>`;
    }
    
    // 下一页
    html += `
        <li class="page-item ${currentPage >= totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="${clickFunction}(${currentPage + 1}); return false;">下一页</a>
        </li>
    `;
    
    return html;
}

// 更新最终结果会话信息
function updateFinalResultSessionInfo(sessionInfo) {
    const { session_id, session_name, session_status } = sessionInfo;
    
    const sessionStatusEl = document.getElementById('finalResultSessionStatus');
    const sessionNameEl = document.getElementById('finalResultSessionName');
    
    if (sessionStatusEl) {
        const statusClass = {
            'draft': 'secondary',
            'published': 'success',
            'executing': 'warning',
            'completed': 'primary',
            'cancelled': 'danger'
        }[session_status] || 'secondary';
        
        sessionStatusEl.className = `badge bg-${statusClass}`;
        sessionStatusEl.textContent = session_status || '未知';
    }
    
    if (sessionNameEl) {
        sessionNameEl.textContent = session_name || session_id || '-';
    }
}

// 发布最终结果到生产表
async function publishFinalResult() {
    try {
        const urlParams = new URLSearchParams(window.location.search);
        const sessionId = urlParams.get('session');
        
        if (!sessionId) {
            showNotification('error', '未找到会话ID，无法发布结果');
            return;
        }
        
        if (!confirm('确定要将最终调整结果发布到生产表吗？\n\n这将替换当前的生产数据，操作不可逆转。')) {
            return;
        }
        
        showNotification('info', '正在发布最终结果到生产表...');
        
        const response = await fetch('/api/v2/production/final-scheduling/publish', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                session_id: sessionId,
                publish_mode: 'replace'
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        if (result.success) {
            const data = result.data;
            showNotification('success', `发布成功！共发布 ${data.affected_rows} 条记录到生产表`);
            
            // 后端已经自动保存Excel文件（如果启用了自动保存）
            // 这里不再需要弹窗询问用户，Excel文件会自动保存到配置的路径
            console.log('✅ 最终结果已发布，Excel自动保存功能已处理');
            
            // 刷新最终结果数据
            loadFinalResultData();
            
        } else {
            throw new Error(result.message || '发布失败');
        }
        
    } catch (error) {
        console.error('❌ 发布最终结果失败:', error);
        showNotification('error', `发布失败: ${error.message}`);
    }
}

// 导出最终结果数据
async function exportFinalResult() {
    try {
        const urlParams = new URLSearchParams(window.location.search);
        const sessionId = urlParams.get('session');
        
        showNotification('info', '正在导出最终结果数据...');
        
        // 获取所有数据（不分页）
        const params = new URLSearchParams({
            page: '1',
            size: '10000'
        });
        
        if (sessionId) params.append('session_id', sessionId);
        
        const response = await fetch(`/api/v2/production/final-scheduling/data?${params.toString()}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        if (result.success && result.data) {
            // 准备导出数据
            const exportData = result.data.map(item => ({
                '内部工单号': item.LOT_ID,
                '产品名称': item.DEVICE,
                '产品ID': item.PROD_ID,
                '数量': item.GOOD_QTY,
                '原分选机': item.ORIGINAL_HANDLER_ID,
                '原优先级': item.ORIGINAL_PRIORITY,
                '来源类型': item.SOURCE_TYPE,
                '调整类型': item.ADJUSTMENT_TYPE,
                '最终优先级': item.FINAL_PRIORITY,
                '最终分选机': item.FINAL_HANDLER_ID,
                '调整原因': item.ADJUSTMENT_REASON,
                '失败原因': item.FAILURE_REASON,
                '调整人': item.ADJUSTED_BY,
                '调整时间': item.ADJUSTED_AT ? new Date(item.ADJUSTED_AT).toLocaleString() : '',
                '会话ID': item.SESSION_ID,
                '会话名称': item.SESSION_NAME,
                '状态': item.STATUS
            }));
            
            // 使用SheetJS导出Excel
            const ws = XLSX.utils.json_to_sheet(exportData);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "最终排产调整结果");
            
            const filename = `最终排产调整结果_${sessionId || 'all'}_${new Date().toISOString().slice(0, 10)}.xlsx`;
            XLSX.writeFile(wb, filename);
            
            showNotification('success', `导出成功！文件已保存为: ${filename}`);
            
        } else {
            throw new Error(result.message || '获取导出数据失败');
        }
        
    } catch (error) {
        console.error('❌ 导出最终结果失败:', error);
        showNotification('error', `导出失败: ${error.message}`);
    }
}

// 导出已发布的排产结果到指定路径
function exportPublishedScheduleWithPathSelection() {
    try {
        // 由于浏览器安全限制，我们提供多种导出方式
        const exportMethod = confirm('选择导出方式：\n\n确定 = 保存到服务器指定路径\n取消 = 浏览器默认下载');
        
        if (exportMethod) {
            // 用户选择保存到指定路径
            const userPath = prompt('请输入完整的文件保存路径（包括文件名）：\n\n例如：C:\\Users\\<USER>\\Desktop\\已发布排产结果.xlsx', 
                `C:\\Users\\<USER>\\Desktop\\已发布排产结果_${new Date().toISOString().slice(0, 10)}.xlsx`);
            
            if (userPath && userPath.trim()) {
                exportPublishedScheduleToPath(userPath.trim());
            } else {
                showNotification('warning', '已取消导出');
            }
        } else {
            // 用户选择浏览器下载
            exportPublishedScheduleAsBrowserDownload();
        }
        
    } catch (error) {
        console.error('导出选择错误:', error);
        // 如果出错，提供手动输入选项
        const userPath = prompt('请手动输入完整的文件保存路径：\n\n例如：C:\\Users\\<USER>\\Desktop\\已发布排产结果.xlsx', 
            `C:\\Users\\<USER>\\Desktop\\已发布排产结果_${new Date().toISOString().slice(0, 10)}.xlsx`);
        
        if (userPath && userPath.trim()) {
            exportPublishedScheduleToPath(userPath.trim());
        } else {
            // 如果用户不输入路径，使用浏览器下载
            exportPublishedScheduleAsBrowserDownload();
        }
    }
}

// 导出已发布的排产结果到指定路径
function exportPublishedScheduleToPath(filePath) {
    if (!filePath || !filePath.trim()) {
        showNotification('error', '请指定有效的文件路径');
        return;
    }
    
    showNotification('info', '正在导出已发布的排产结果到指定路径...');
    
    fetch('/api/v2/production/export-schedule-to-path', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            export_path: filePath.trim()
        })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showNotification('success', `已成功导出 ${result.records_count} 条记录到：\n${result.export_path}`);
        } else {
            throw new Error(result.message || '导出失败');
        }
    })
    .catch(error => {
        console.error('导出到指定路径失败:', error);
        showNotification('error', `无法保存到指定路径：${error.message}`);
        
        // 提供备选方案
        if (confirm('是否改用浏览器默认下载？')) {
            exportPublishedScheduleAsBrowserDownload();
        }
    });
}

// 已发布排产结果的浏览器默认下载方式（备选方案）
function exportPublishedScheduleAsBrowserDownload() {
    // 从数据库获取最新的排产数据并使用前端导出
    fetch('/api/v2/production/done-lots?size=10000', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.success && result.data && result.data.length > 0) {
            const scheduleData = result.data;
            
            // 使用与exportData()相同的格式
            const exportData = scheduleData.map(item => ({
                '优先级': item.PRIORITY || '',
                '内部工单号': item.LOT_ID || '',
                '产品名称': item.DEVICE || '',
                '芯片名称': item.CHIP_ID || '',
                '分选机ID': item.HANDLER_ID || '',
                '良品数量': item.GOOD_QTY || 0,
                '综合评分': item.comprehensive_score || 0.0,
                '工序': item.STAGE || '',
                '工步': item.STEP || '',
                '封装类型': item.PKG_PN || '',
                '创建时间': item.CREATE_TIME || ''
            }));
            
            // 使用SheetJS导出
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(exportData);
            XLSX.utils.book_append_sheet(wb, ws, "已发布排产批次");
            
            const filename = `已发布排产结果_${new Date().toISOString().slice(0, 10)}.xlsx`;
            XLSX.writeFile(wb, filename);
            
            showNotification('success', `已使用浏览器下载：${filename}`);
        } else {
            throw new Error('没有找到已发布的排产数据');
        }
    })
    .catch(error => {
        console.error('浏览器下载失败:', error);
        showNotification('error', `浏览器下载失败：${error.message}`);
    });
}

function cancelAdjustments() {
    if (adjustmentManager.isModified) {
        if (confirm('确定要取消所有调整吗？未保存的修改将丢失。')) {
            // 重置数据
            adjustmentManager.currentData = [...adjustmentManager.originalData];
            groupedData = adjustmentManager.groupDataByHandler(adjustmentManager.currentData);
            adjustmentManager.operationHistory = [];
            adjustmentManager.historyIndex = -1;
            adjustmentManager.isModified = false;
            
            renderHandlerGroups();
            updateAdjustmentStatistics();
            adjustmentManager.updateHistoryUI();
            
            showNotification('info', '已取消所有调整');
        }
    } else {
        // 切换回查看模式
        document.getElementById('viewModeBtn').checked = true;
        switchToViewMode();
    }
}

// ====== 全局操作历史控制函数 - Phase 3.1 ======

// 全局撤销函数
function undo() {
    if (adjustmentManager && adjustmentManager.canUndo()) {
        adjustmentManager.undo();
    } else {
        showNotification('warning', '没有可撤销的操作');
    }
}

// 全局重做函数
function redo() {
    if (adjustmentManager && adjustmentManager.canRedo()) {
        adjustmentManager.redo();
    } else {
        showNotification('warning', '没有可重做的操作');
    }
}

// 清除操作历史
function clearOperationHistory() {
    if (adjustmentManager) {
        if (confirm('确定要清除所有操作历史吗？此操作不可撤销。')) {
            adjustmentManager.clearHistory();
            showNotification('info', '操作历史已清除');
        }
    }
}

// 显示操作历史摘要
function showHistorySummary() {
    if (adjustmentManager) {
        const summary = adjustmentManager.getHistorySummary();
        
        const summaryText = `
操作历史摘要：
- 总操作数：${summary.totalOperations}
- 当前位置：${summary.currentPosition}/${summary.totalOperations}
- 可撤销：${summary.canUndo ? '是' : '否'}
- 可重做：${summary.canRedo ? '是' : '否'}

最近操作：
${summary.recentOperations.map((op, index) => 
    `${index + 1}. ${op.description} (${new Date(op.timestamp).toLocaleTimeString()})`
).join('\n')}
        `;
        
        alert(summaryText);
    }
}

// 键盘快捷键支持
document.addEventListener('keydown', function(e) {
    // 只在调整模式下启用快捷键
    if (currentMode !== 'adjust') return;
    
    // Ctrl+Z 撤销
    if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
        e.preventDefault();
        undo();
    }
    
    // Ctrl+Shift+Z 或 Ctrl+Y 重做
    if ((e.ctrlKey && e.shiftKey && e.key === 'Z') || (e.ctrlKey && e.key === 'y')) {
        e.preventDefault();
        redo();
    }
    
    // Ctrl+S 保存
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        saveAdjustments();
    }
    
    // Escape 取消选择
    if (e.key === 'Escape') {
        clearLotSelection();
    }
});

// 更新操作历史状态显示
function updateOperationHistoryStatus() {
    if (adjustmentManager) {
        const summary = adjustmentManager.getHistorySummary();
        
        // 更新按钮状态
        const undoBtn = document.getElementById('undoBtn');
        const redoBtn = document.getElementById('redoBtn');
        
        if (undoBtn) {
            undoBtn.disabled = !summary.canUndo;
            undoBtn.title = summary.canUndo ? '撤销最后操作 (Ctrl+Z)' : '没有可撤销的操作';
        }
        
        if (redoBtn) {
            redoBtn.disabled = !summary.canRedo;
            redoBtn.title = summary.canRedo ? '重做操作 (Ctrl+Shift+Z)' : '没有可重做的操作';
        }
        
        // 更新操作计数显示
        const operationCount = document.getElementById('operationCount');
        if (operationCount) {
            operationCount.textContent = `${summary.currentPosition}/${summary.totalOperations}`;
        }
    }
}

// 初始化操作历史功能的定时更新
setInterval(updateOperationHistoryStatus, 1000);

console.log('✅ 已排产批次页面脚本加载完成');
console.log('🔧 调整功能模块已加载');
console.log('📝 操作历史功能已加载');
</script>

<!-- 引入 Sortable.js CDN 拖拽库 -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>

<script>
// ====== 拖拽管理器 - Phase 2 核心功能 ======

class DragDropManager {
    constructor(adjustmentManager) {
        this.adjustmentManager = adjustmentManager;
        this.sortableInstances = [];
        this.isDragActive = false;
        this.dragStartHandler = null;
        this.dragEndHandler = null;
    }
    
    // 初始化拖拽功能
    initializeDragAndDrop() {
        console.log('🚀 初始化Sortable.js拖拽功能...');
        
        // 清除旧的拖拽实例
        this.destroyDragInstances();
        
        // 为每个分选机分组初始化拖拽
        const sortableContainers = document.querySelectorAll('.lot-list-sortable');
        
        sortableContainers.forEach(container => {
            const handlerId = container.closest('.handler-group-card')?.dataset.handlerId;
            
            if (!handlerId) return;
            
            const sortable = new Sortable(container, {
                group: {
                    name: 'lot-adjustment',
                    pull: true,
                    put: true
                },
                animation: 200,
                ghostClass: 'lot-card-ghost',
                chosenClass: 'lot-card-chosen',
                dragClass: 'lot-card-drag',
                forceFallback: true,
                fallbackClass: 'lot-card-fallback',
                fallbackOnBody: true,
                swapThreshold: 0.65,
                sort: true, // 明确启用排序
                disabled: false, // 确保拖拽未被禁用
                
                // 拖拽开始事件
                onStart: (evt) => this.handleDragStart(evt),
                
                // 拖拽结束事件
                onEnd: (evt) => this.handleDragEnd(evt),
                
                // 拖拽移动事件
                onMove: (evt) => this.handleDragMove(evt),
                
                // 添加事件（从其他容器拖入）
                onAdd: (evt) => this.handleDragAdd(evt),
                
                // 更新事件（容器内重排序）
                onUpdate: (evt) => this.handleDragUpdate(evt),
                
                // 排序事件（更通用的排序处理）
                onSort: (evt) => {
                    console.log(`🔀 排序事件触发: ${evt.from === evt.to ? '同容器内排序' : '跨容器移动'}`);
                    if (evt.from === evt.to) {
                        // 同容器内排序，确保触发update处理
                        this.handleDragUpdate(evt);
                    }
                }
            });
            
            this.sortableInstances.push({
                sortable: sortable,
                handlerId: handlerId,
                container: container
            });
        });
        
        console.log(`✅ 已为 ${this.sortableInstances.length} 个分选机分组初始化拖拽功能`);
        
        // 添加视觉样式
        this.addDragStyles();
    }
    
    // 拖拽开始处理
    handleDragStart(evt) {
        this.isDragActive = true;
        const lotId = evt.item.dataset.lotId;
        const fromHandlerId = evt.from.closest('.handler-group-card')?.dataset.handlerId;
        
        console.log(`🖱️ 开始拖拽批次: ${lotId} from ${fromHandlerId}`);
        
        // 添加拖拽状态样式
        document.body.classList.add('dragging-active');
        evt.item.classList.add('being-dragged');
        
        // 高亮可投放区域
        this.highlightDropTargets(evt.item);
        
        // 记录拖拽开始状态
        this.dragStartHandler = fromHandlerId;
        
        // 显示拖拽提示
        this.showDragHint(lotId);
    }
    
    // 拖拽结束处理
    handleDragEnd(evt) {
        this.isDragActive = false;
        const lotId = evt.item.dataset.lotId;
        const fromHandlerId = this.dragStartHandler;
        const toHandlerId = evt.to.closest('.handler-group-card')?.dataset.handlerId;
        
        console.log(`🖱️ 拖拽结束: ${lotId} from ${fromHandlerId} to ${toHandlerId}`);
        
        // 移除拖拽状态样式
        document.body.classList.remove('dragging-active');
        evt.item.classList.remove('being-dragged');
        this.clearDropTargetHighlights();
        this.hideDragHint();
        
        // 如果是跨分选机移动
        if (fromHandlerId !== toHandlerId) {
            this.handleCrossMachineMove(lotId, fromHandlerId, toHandlerId, evt);
        }
        
        // 重新计算优先级（这会触发界面重新渲染）
        this.recalculatePriorities(evt.to, toHandlerId);
        
        // 记录操作历史
        this.recordDragOperation(lotId, fromHandlerId, toHandlerId, evt);
    }
    
    // 拖拽移动检查
    handleDragMove(evt) {
        const fromHandlerId = evt.from.closest('.handler-group-card')?.dataset.handlerId;
        const toHandlerId = evt.to.closest('.handler-group-card')?.dataset.handlerId;
        
        // 可以添加移动限制逻辑
        if (this.shouldPreventMove(fromHandlerId, toHandlerId)) {
            return false; // 阻止移动
        }
        
        return true; // 允许移动
    }
    
    // 添加到新容器处理
    handleDragAdd(evt) {
        const lotId = evt.item.dataset.lotId;
        const toHandlerId = evt.to.closest('.handler-group-card')?.dataset.handlerId;
        
        console.log(`➕ 批次 ${lotId} 添加到 ${toHandlerId}`);
        
        // 更新数据
        this.updateLotHandler(lotId, toHandlerId);
    }
    
    // 容器内更新处理
    handleDragUpdate(evt) {
        const handlerId = evt.from.closest('.handler-group-card')?.dataset.handlerId;
        const lotId = evt.item.dataset.lotId;
        
        console.log(`🔄 同机台内排序: ${lotId} 在 ${handlerId} 从位置 ${evt.oldIndex} → ${evt.newIndex}`);
        
        // 重新计算此分选机内的优先级（这会触发界面重新渲染）
        this.recalculatePriorities(evt.from, handlerId);
        
        // 记录操作历史（避免在recordDragOperation中重复记录）
        this.adjustmentManager.addOperation({
            type: 'drag_reorder',
            description: `在分选机 ${handlerId} 内重新排序批次 ${lotId}`,
            lotId: lotId,
            handlerId: handlerId,
            oldIndex: evt.oldIndex,
            newIndex: evt.newIndex,
            timestamp: new Date().toISOString()
        });
        
        // 标记为已修改
        this.adjustmentManager.isModified = true;
        
        // 简化操作提示 - 只在控制台记录，避免过多弹窗
        console.log(`✅ 已调整批次 ${lotId} 在 ${handlerId} 内的优先级`);
    }
    
    // 跨分选机移动处理
    handleCrossMachineMove(lotId, fromHandlerId, toHandlerId, evt) {
        console.log(`🔄 跨机移动: ${lotId} ${fromHandlerId} → ${toHandlerId}`);
        
        // 显示跨机移动确认
        this.showCrossMachineConfirmation(lotId, fromHandlerId, toHandlerId, () => {
            // 确认后执行移动
            this.executeCrossMachineMove(lotId, fromHandlerId, toHandlerId);
        });
    }
    
    // 显示跨机移动确认
    showCrossMachineConfirmation(lotId, fromHandlerId, toHandlerId, onConfirm) {
        const fromName = fromHandlerId === 'UNASSIGNED' ? '失败列表' : fromHandlerId;
        const toName = toHandlerId === 'UNASSIGNED' ? '失败列表' : toHandlerId;
        
        // 创建确认对话框
        const confirmDialog = document.createElement('div');
        confirmDialog.className = 'cross-machine-confirm';
        confirmDialog.innerHTML = `
            <div class="confirm-overlay">
                <div class="confirm-dialog">
                    <div class="confirm-header">
                        <h5><i class="fas fa-exchange-alt me-2"></i>跨分选机移动确认</h5>
            </div>
                    <div class="confirm-body">
                        <p>您正在将批次 <strong>${lotId}</strong> 从</p>
                        <div class="move-visualization">
                            <div class="from-box">${fromName}</div>
                            <i class="fas fa-arrow-right move-arrow"></i>
                            <div class="to-box">${toName}</div>
                        </div>
                        <p>这将改变该批次的分选机分配，确定要继续吗？</p>
                    </div>
                    <div class="confirm-actions">
                        <button type="button" class="btn btn-secondary btn-sm me-2" onclick="this.closest('.cross-machine-confirm').remove()">
                            <i class="fas fa-times me-1"></i>取消
                        </button>
                        <button type="button" class="btn btn-primary btn-sm" onclick="confirmCrossMove('${lotId}', '${fromHandlerId}', '${toHandlerId}')">
                            <i class="fas fa-check me-1"></i>确认移动
                        </button>
                    </div>
                </div>
        </div>
    `;
    
        // 添加样式
        if (!document.getElementById('crossMachineStyles')) {
            const style = document.createElement('style');
            style.id = 'crossMachineStyles';
            style.textContent = `
                .cross-machine-confirm {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 20000;
                }
                
                .confirm-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    animation: fadeIn 0.3s ease;
                }
                
                .confirm-dialog {
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 8px 24px rgba(0,0,0,0.2);
                    max-width: 400px;
                    width: 90%;
                    animation: slideIn 0.3s ease;
                }
                
                .confirm-header {
                    padding: 20px 20px 10px;
                    border-bottom: 1px solid #eee;
                }
                
                .confirm-header h5 {
                    margin: 0;
                    color: #333;
                }
                
                .confirm-body {
                    padding: 20px;
                    text-align: center;
                }
                
                .move-visualization {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 20px 0;
                    padding: 15px;
                    background: #f8f9fa;
                    border-radius: 8px;
                }
                
                .from-box, .to-box {
                    padding: 8px 16px;
                    border-radius: 6px;
                    font-weight: bold;
                    min-width: 120px;
                }
                
                .from-box {
                    background: #ffeaa7;
                    color: #2d3436;
                }
                
                .to-box {
                    background: #a7f7a7;
                    color: #2d3436;
                }
                
                .move-arrow {
                    margin: 0 15px;
                    color: #0984e3;
                    font-size: 20px;
                    animation: pulse 1s infinite;
                }
                
                .confirm-actions {
                    padding: 10px 20px 20px;
                    text-align: right;
                }
                
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
                
                @keyframes slideIn {
                    from { transform: scale(0.9) translateY(-20px); opacity: 0; }
                    to { transform: scale(1) translateY(0); opacity: 1; }
                }
                
                @keyframes pulse {
                    0%, 100% { transform: scale(1); }
                    50% { transform: scale(1.1); }
                }
            `;
            document.head.appendChild(style);
        }
        
        // 存储确认回调
        window.confirmCrossMove = (lotId, fromHandlerId, toHandlerId) => {
            document.querySelector('.cross-machine-confirm').remove();
            onConfirm();
        };
        
        document.body.appendChild(confirmDialog);
        
        // 点击背景关闭
        confirmDialog.querySelector('.confirm-overlay').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                confirmDialog.remove();
            }
        });
    }
    
    // 执行跨机移动
    executeCrossMachineMove(lotId, fromHandlerId, toHandlerId) {
        // 更新数据中的分选机ID
        this.updateLotHandler(lotId, toHandlerId);
        
        // 如果是从失败列表移动到正常分选机
        if (fromHandlerId === 'UNASSIGNED' && toHandlerId !== 'UNASSIGNED') {
            this.recoverFailedLot(lotId);
        }
        
        // 更新负载显示
        this.updateHandlerLoad(fromHandlerId);
        this.updateHandlerLoad(toHandlerId);
        
        // 记录移动成功 - 在控制台而不是弹窗提示
        const fromName = fromHandlerId === 'UNASSIGNED' ? '失败列表' : fromHandlerId;
        const toName = toHandlerId === 'UNASSIGNED' ? '失败列表' : toHandlerId;
        console.log(`✅ 批次 ${lotId} 已从 ${fromName} 移动到 ${toName}`);
    }
    
    // 重新计算优先级
    recalculatePriorities(container, handlerId) {
        const lotCards = container.querySelectorAll('.lot-card');
        
        console.log(`🔢 重新计算 ${handlerId} 的 ${lotCards.length} 个批次优先级...`);
        
        const priorityChanges = [];
        
        lotCards.forEach((card, index) => {
            const lotId = card.dataset.lotId;
            const newPriority = index + 1;
            
            // 获取旧优先级
            const lotIndex = currentData.findIndex(lot => (lot.LOT_ID || lot.lot_id) === lotId);
            const oldPriority = lotIndex !== -1 ? (currentData[lotIndex].PRIORITY || currentData[lotIndex].priority) : null;
            
            // 更新数据
            if (lotIndex !== -1) {
                currentData[lotIndex].PRIORITY = newPriority;
                currentData[lotIndex].priority = newPriority;
                
                if (oldPriority !== newPriority) {
                    priorityChanges.push({ lotId, oldPriority, newPriority });
                }
            }
        });
        
        // 只记录实际有变化的优先级调整
        if (priorityChanges.length > 0) {
            console.log(`📊 优先级变化详情:`, priorityChanges);
        }
        
        console.log(`✅ 已重新计算 ${handlerId} 的 ${lotCards.length} 个批次优先级，其中 ${priorityChanges.length} 个有变化`);
        
        // 实时更新优先级显示，无需重建整个DOM
        this.updatePriorityDisplay(container);
        
        // 更新统计信息
        updateAdjustmentStatistics();
    }
    
    // 实时更新优先级显示（无需重建DOM）
    updatePriorityDisplay(container) {
        const lotCards = container.querySelectorAll('.lot-card');
        
        lotCards.forEach((card, index) => {
            const priorityBadge = card.querySelector('.lot-priority-badge');
            const newPriority = index + 1;
            
            if (priorityBadge && !card.classList.contains('failed-lot')) {
                // 更新优先级显示
                priorityBadge.textContent = newPriority;
                
                // 添加更新动画效果
                priorityBadge.style.animation = 'pulse 0.6s ease';
                setTimeout(() => {
                    priorityBadge.style.animation = '';
                }, 600);
            }
        });
        
        console.log(`✅ 已更新容器内 ${lotCards.length} 个批次的优先级显示`);
    }
    
    // 拖拽操作后刷新界面
    refreshAfterDrag() {
        // 重新分组数据
        if (adjustmentManager) {
            groupedData = adjustmentManager.groupDataByHandler(currentData);
        }
        
        // 重新渲染分组界面（会自动重新初始化拖拽功能）
        renderHandlerGroups();
        
        // 更新统计信息
        updateAdjustmentStatistics();
        
        console.log('🔄 拖拽后界面已完全刷新');
    }
    

    
    // 更新批次分选机
    updateLotHandler(lotId, newHandlerId) {
        const lotIndex = currentData.findIndex(lot => (lot.LOT_ID || lot.lot_id) === lotId);
        
        if (lotIndex !== -1) {
            // 更新数据
            currentData[lotIndex].HANDLER_ID = newHandlerId;
            currentData[lotIndex].handler_id = newHandlerId;
            
            // 标记已修改
            this.adjustmentManager.isModified = true;
            
            console.log(`✅ 已更新批次 ${lotId} 的分选机为 ${newHandlerId}`);
        }
    }
    
    // 恢复失败批次
    recoverFailedLot(lotId) {
        const lotIndex = currentData.findIndex(lot => (lot.LOT_ID || lot.lot_id) === lotId);
        
        if (lotIndex !== -1) {
            currentData[lotIndex].is_failed = false;
            currentData[lotIndex].status = 'scheduled';
            currentData[lotIndex].WIP_STATE = 'SCHEDULED';
            
            console.log(`✅ 已恢复失败批次 ${lotId}`);
        }
    }
    
    // 高亮投放目标
    highlightDropTargets(draggedItem) {
        const allHandlerCards = document.querySelectorAll('.handler-group-card');
        
        allHandlerCards.forEach(card => {
            card.classList.add('drop-target-highlight');
        });
    }
    
    // 清除投放目标高亮
    clearDropTargetHighlights() {
        const allHandlerCards = document.querySelectorAll('.handler-group-card');
        
        allHandlerCards.forEach(card => {
            card.classList.remove('drop-target-highlight');
        });
    }
    
    // 显示拖拽提示
    showDragHint(lotId) {
        const hintElement = document.createElement('div');
        hintElement.id = 'dragHint';
        hintElement.className = 'drag-hint';
        hintElement.innerHTML = `
            <i class="fas fa-hand-rock me-2"></i>
            正在拖拽批次 <strong>${lotId}</strong>
            <br><small>拖拽到目标分选机进行调整</small>
        `;
        
        document.body.appendChild(hintElement);
    }
    
    // 隐藏拖拽提示
    hideDragHint() {
        const hintElement = document.getElementById('dragHint');
        if (hintElement) {
            hintElement.remove();
        }
    }
    
    // 记录拖拽操作（只记录跨机移动，同机台重排序在handleDragUpdate中已记录）
    recordDragOperation(lotId, fromHandlerId, toHandlerId, evt) {
        if (fromHandlerId !== toHandlerId) {
            this.adjustmentManager.addOperation({
                type: 'drag_move',
                description: `拖拽移动批次 ${lotId} 从 ${fromHandlerId} 到 ${toHandlerId}`,
                lotId: lotId,
                oldHandler: fromHandlerId,
                newHandler: toHandlerId,
                timestamp: Date.now()
            });
        }
        // 注意：同机台内重排序已在handleDragUpdate中记录，此处不再重复记录
    }
    
    // 批量保存调整到后端
    async saveBatchAdjustments() {
        if (!this.adjustmentManager.isModified) {
            showNotification('info', '没有需要保存的修改');
            return false;
        }
        
        try {
            // 收集所有需要更新的数据
            const updates = this.collectUpdateData();
            
            if (updates.length === 0) {
                showNotification('warning', '没有找到需要保存的更改');
                return false;
            }
            
            // 检查是否需要分批处理
            const maxBatchSize = 100; // 前端分批大小
            if (updates.length > maxBatchSize) {
                return await this.saveBatchAdjustmentsInChunks(updates, maxBatchSize);
            }
            
            return await this.saveSingleBatch(updates);
            
        } catch (error) {
            console.error('保存调整失败:', error);
            showNotification('error', `保存失败: ${error.message}`);
            
            // 保存失败后确保拖拽功能正常
            if (dragDropManager) {
                console.log('🔄 保存失败，重新初始化拖拽功能...');
                setTimeout(() => {
                    dragDropManager.initializeDragAndDrop();
                }, 1000);
            }
            
            return false;
        }
    }
    
    // 单批次保存
    async saveSingleBatch(updates) {
        showNotification('info', `正在保存 ${updates.length} 项更改...`);
        
        // 添加详细调试信息
        console.log('📤 发送保存请求:', {
            updates: updates,
            operation_type: 'drag_adjustment',
            session_id: Date.now().toString()
        });
        
        // 调用后端API
        const response = await fetch('/api/v2/production/manual-adjustment/batch-update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                updates: updates,
                operation_type: 'drag_adjustment',
                session_id: Date.now().toString()
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        console.log('📥 API响应:', {
            status: response.status,
            success: result.success,
            message: result.message,
            data: result
        });
        
        if (result.success) {
            const updatedCount = result.updated_count || 0;
            const totalCount = result.total_count || 0;
            const failedCount = totalCount - updatedCount;
            
            if (updatedCount > 0 && failedCount === 0) {
                showNotification('success', `保存成功: 已更新 ${updatedCount} 条记录`);
            } else if (updatedCount > 0 && failedCount > 0) {
                showNotification('warning', `部分保存成功: 已更新 ${updatedCount} 条记录，${failedCount} 条失败批次已跳过`);
            } else if (updatedCount === 0 && failedCount > 0) {
                showNotification('info', `无需更新: ${failedCount} 条失败批次已跳过，其他记录都是最新状态`);
            } else {
                showNotification('info', '所有记录都是最新状态，无需更新');
            }
            
            console.log(`✅ 保存结果: ${updatedCount}/${totalCount} 条记录已更新`);
            
            // 如果有失败的更新，显示详细信息
            if (result.failed_updates && result.failed_updates.length > 0) {
                console.log('⚠️ 跳过的批次:', result.failed_updates);
            }
            
            // 重置修改状态
            this.adjustmentManager.isModified = false;
            this.adjustmentManager.operationHistory = [];
            this.adjustmentManager.historyIndex = -1;
            this.adjustmentManager.updateHistoryUI();
            
            // 刷新数据
            await this.refreshDataAfterSave();
            
            return true;
        } else {
            console.error('❌ 保存失败:', result);
            showNotification('error', `保存失败: ${result.message}`);
            if (result.errors) {
                console.error('错误详情:', result.errors);
            }
            return false;
        }
    }
    
    // 分批次保存
    async saveBatchAdjustmentsInChunks(updates, chunkSize) {
        const chunks = [];
        for (let i = 0; i < updates.length; i += chunkSize) {
            chunks.push(updates.slice(i, i + chunkSize));
        }
        
        showNotification('info', `正在保存 ${updates.length} 条调整记录...`);
        
        let totalUpdated = 0;
        let totalFailed = 0;
        
        for (let i = 0; i < chunks.length; i++) {
            const chunk = chunks[i];
            const chunkNum = i + 1;
            
            try {
                // 静默处理批次保存，不显示每个批次的进度
                
                const response = await fetch('/api/v2/production/manual-adjustment/batch-update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        updates: chunk,
                        operation_type: 'drag_adjustment',
                        session_id: `${Date.now()}_batch_${chunkNum}`
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                
                if (result.success) {
                    totalUpdated += result.updated_count || 0;
                    console.log(`✅ 第 ${chunkNum} 批保存成功: ${result.updated_count}/${result.total_count}`);

                    } else {
                    totalFailed += chunk.length;
                    console.error(`❌ 第 ${chunkNum} 批保存失败:`, result.message);
                }
                
                // 添加延迟避免服务器压力
                if (i < chunks.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
                
            } catch (error) {
                totalFailed += chunk.length;
                console.error(`❌ 第 ${chunkNum} 批保存异常:`, error);
            }
        }
        
        // 汇总结果
        const hasFailures = totalFailed > 0;
        const hasUpdates = totalUpdated > 0;
        const isSuccess = !hasFailures;
        const originalTotal = updates.length;
        const skippedFailedLots = originalTotal - totalUpdated - totalFailed;
        
        if (hasUpdates && !hasFailures && skippedFailedLots === 0) {
            showNotification('success', `保存完成: 成功更新 ${totalUpdated} 条记录`);
        } else if (hasUpdates && !hasFailures && skippedFailedLots > 0) {
            showNotification('warning', `部分保存成功: 成功更新 ${totalUpdated} 条记录，${skippedFailedLots} 条失败批次已跳过`);
        } else if (hasUpdates && hasFailures) {
            showNotification('warning', `保存部分成功: 成功 ${totalUpdated} 条，失败 ${totalFailed} 条，跳过 ${skippedFailedLots} 条失败批次`);
        } else if (!hasUpdates && !hasFailures && skippedFailedLots > 0) {
            showNotification('info', `无需更新: ${skippedFailedLots} 条失败批次已跳过，其他记录都是最新状态`);
        } else if (!hasUpdates && !hasFailures) {
            showNotification('info', '没有需要更新的数据，所有记录都是最新状态');
        } else if (hasFailures && !hasUpdates) {
            showNotification('error', `保存失败: 共 ${totalFailed} 条记录保存失败`);
        } else {
            showNotification('error', `保存失败: 共 ${totalFailed} 条记录保存失败`);
        }
            
            // 重置修改状态
            this.adjustmentManager.isModified = false;
            this.adjustmentManager.operationHistory = [];
            this.adjustmentManager.historyIndex = -1;
            this.adjustmentManager.updateHistoryUI();
            
            // 刷新数据
            await this.refreshDataAfterSave();
        
        return isSuccess;
    }
    
    // 收集更新数据
    collectUpdateData() {
        const updates = [];
        
        console.log('🔍 开始收集更新数据，当前分组数据:', groupedData);
        
        Object.entries(groupedData).forEach(([handlerId, group]) => {
            group.lots.forEach((lot, index) => {
                const lotId = lot.LOT_ID || lot.lot_id;
                
                // 验证LOT_ID是否有效
                if (!lotId || lotId.trim() === '') {
                    console.warn('⚠️ 发现无效的LOT_ID:', lot);
                    return; // 跳过无效的记录
                }
                
                // 检查是否为失败批次，失败批次不保存到数据库
                if (lot.is_failed === true || lot.status === 'failed' || handlerId === 'UNASSIGNED') {
                    console.log(`🚫 跳过失败批次 ${lotId}，失败批次不存在于数据库中`);
                    return;
                }
                
                const newPriority = index + 1;
                const currentHandlerId = lot.HANDLER_ID || lot.handler_id;
                const originalPriority = lot.original_priority || lot.PRIORITY || lot.priority;
                
                // 检查是否有变化
                const priorityChanged = originalPriority !== newPriority;
                const handlerChanged = currentHandlerId !== handlerId;
                
                if (priorityChanged || handlerChanged) {
                    const updateItem = {
                        lot_id: lotId,
                        priority: newPriority,
                        handler_id: handlerId,
                        old_handler_id: currentHandlerId,
                        old_priority: originalPriority,
                        quantity: lot.GOOD_QTY || lot.good_qty || 0
                    };
                    
                    console.log(`📝 添加更新项:`, updateItem);
                    updates.push(updateItem);
                }
            });
        });
        
        console.log(`✅ 收集到 ${updates.length} 项更新`, updates);
        return updates;
    }
    
    // 刷新数据
    async refreshDataAfterSave() {
        try {
            // 重新加载数据
            if (typeof loadAdjustmentData === 'function') {
                await loadAdjustmentData();
            } else {
                // 重新分组现有数据
                groupedData = this.adjustmentManager.groupDataByHandler(currentData);
                renderHandlerGroups();
                updateAdjustmentStatistics();
            }
            
            // 数据已刷新，不显示额外提示以避免重复
        } catch (error) {
            console.error('刷新数据失败:', error);
            showNotification('warning', '数据保存成功，但刷新失败，请手动刷新页面');
        }
    }
    
    // 实时验证调整
    async validateAdjustmentAPI(updates = null) {
        try {
            const updateData = updates || this.collectUpdateData();
            
            if (updateData.length === 0) {
                return { valid: true, message: '没有需要验证的更改' };
            }
            
            const response = await fetch('/api/v2/production/manual-adjustment/validate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    updates: updateData
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                return {
                    valid: true,
                    validation_result: result.validation_result
                };
            } else {
                return {
                    valid: false,
                    message: result.message
                };
            }
            
        } catch (error) {
            console.error('验证调整失败:', error);
            return {
                valid: false,
                message: `验证失败: ${error.message}`
            };
        }
    }
    
    // 更新负载可视化
    updateLoadVisualization() {
        Object.keys(groupedData).forEach(handlerId => {
            this.updateHandlerLoad(handlerId);
        });
    }
    
    // 更新单个分选机负载
    updateHandlerLoad(handlerId) {
        const handlerCard = document.querySelector(`[data-handler-id="${handlerId}"]`);
        if (!handlerCard) return;
        
        const load = this.adjustmentManager.calculateHandlerLoad(handlerId);
        const progressBar = handlerCard.querySelector('.load-progress-bar');
        const percentageText = handlerCard.querySelector('.load-percentage');
        
        if (progressBar && percentageText) {
            progressBar.style.width = `${load.percentage}%`;
            percentageText.textContent = `${load.percentage}%`;
            
            // 更新颜色
            progressBar.className = 'progress-bar';
            if (load.percentage > 90) {
                progressBar.classList.add('bg-danger');
            } else if (load.percentage > 75) {
                progressBar.classList.add('bg-warning');
            } else if (load.percentage > 50) {
                progressBar.classList.add('bg-info');
            } else {
                progressBar.classList.add('bg-success');
            }
        }
    }
    
    // 验证调整合理性
    validateAdjustment() {
        // 这里可以添加实时验证逻辑
        console.log('🔍 执行拖拽操作验证...');
        
        // 检查优先级连续性
        let hasErrors = false;
        
        Object.keys(groupedData).forEach(handlerId => {
            const lots = groupedData[handlerId].lots;
            for (let i = 0; i < lots.length; i++) {
                const expectedPriority = i + 1;
                const actualPriority = lots[i].PRIORITY || lots[i].priority;
                
                if (actualPriority !== expectedPriority) {
                    console.warn(`⚠️ 优先级不连续: ${handlerId} - 批次 ${lots[i].LOT_ID || lots[i].lot_id}`);
                    hasErrors = true;
                }
            }
        });
        
        if (!hasErrors) {
            console.log('✅ 拖拽操作验证通过');
        }
    }
    
    // 执行异步验证
    performAsyncValidation() {
        if (validationManager) {
            // 使用防抖验证，避免频繁调用
            validationManager.debounceValidation();
        } else {
            console.warn('⚠️ 验证管理器未初始化');
        }
    }
    
    // 是否应该阻止移动
    shouldPreventMove(fromHandlerId, toHandlerId) {
        // 可以在这里添加业务规则限制
        // 例如：某些产品类型不能分配给特定分选机
        return false;
    }
    
    // 销毁拖拽实例
    destroyDragInstances() {
        this.sortableInstances.forEach(instance => {
            if (instance.sortable) {
                instance.sortable.destroy();
            }
        });
        this.sortableInstances = [];
    }
    
    // 添加拖拽样式
    addDragStyles() {
        if (document.getElementById('dragStyles')) return;
        
        const style = document.createElement('style');
        style.id = 'dragStyles';
        style.textContent = `
            /* 拖拽状态样式 */
            .dragging-active {
                cursor: grabbing !important;
            }
            
            .lot-card-ghost {
                opacity: 0.5;
                transform: rotate(2deg);
            }
            
            .lot-card-chosen {
                background-color: #e3f2fd !important;
                border: 2px solid #2196f3 !important;
                transform: scale(1.02);
                box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
            }
            
            .lot-card-drag {
                background-color: #fff3e0 !important;
                border: 2px dashed #ff9800 !important;
                transform: rotate(1deg);
            }
            
            .lot-card-fallback {
                background-color: #f3e5f5 !important;
                border: 2px solid #9c27b0 !important;
            }
            
            .being-dragged {
                z-index: 9999 !important;
                box-shadow: 0 8px 16px rgba(0,0,0,0.3) !important;
            }
            
            .drop-target-highlight {
                border: 2px dashed #4caf50 !important;
                background-color: rgba(76, 175, 80, 0.05) !important;
                position: relative;
            }
            
            .drop-target-highlight::before {
                content: "🎯 拖拽到此处";
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(76, 175, 80, 0.9);
                color: white;
                padding: 8px 16px;
                border-radius: 20px;
                font-size: 14px;
                font-weight: bold;
                z-index: 10;
                pointer-events: none;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            }
            
            .handler-group-card.drag-over {
                border: 3px solid #2196f3 !important;
                background-color: rgba(33, 150, 243, 0.1) !important;
                transform: scale(1.02);
                transition: all 0.2s ease;
            }
            
            .handler-group-card.drag-over::before {
                content: "✅ 放置到这里";
                position: absolute;
                top: 10px;
                right: 10px;
                background: #2196f3;
                color: white;
                padding: 4px 12px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: bold;
                z-index: 10;
                animation: bounce 0.6s infinite alternate;
            }
            
            @keyframes bounce {
                0% { transform: translateY(0); }
                100% { transform: translateY(-3px); }
            }
            
            .drag-hint {
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-size: 14px;
                text-align: center;
                max-width: 250px;
                animation: slideInRight 0.3s ease;
            }
            
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        
        document.head.appendChild(style);
    }
    

}

// 全局拖拽管理器实例
let dragDropManager = null;

// 当切换到调整模式时初始化拖拽功能（防止重复初始化）
let dragFeaturesInitialized = false;

function initializeDragFeatures() {
    if (!adjustmentManager) {
        console.error('❌ AdjustmentManager 未初始化');
        return;
    }
    
    if (dragFeaturesInitialized) {
        console.log('⚠️ 拖拽功能已初始化，跳过重复初始化');
        return;
    }
    
    console.log('🎯 开始初始化拖拽功能...');
    
    if (!dragDropManager) {
        dragDropManager = new DragDropManager(adjustmentManager);
    }
    
    // 初始化验证管理器
    if (!validationManager) {
        validationManager = new ValidationManager(adjustmentManager, dragDropManager);
        console.log('✅ 验证管理器初始化完成');
    }
    
    // 初始化拖拽功能
    setTimeout(() => {
        dragDropManager.initializeDragAndDrop();
        dragFeaturesInitialized = true;
        console.log('✅ 拖拽功能初始化完成');
    }, 500); // 等待DOM渲染完成
}

// 修改切换到调整模式的函数
const originalSwitchToAdjustMode = switchToAdjustMode;
switchToAdjustMode = function() {
    originalSwitchToAdjustMode.call(this);
    
    // 延迟初始化拖拽功能
    setTimeout(() => {
        initializeDragFeatures();
    }, 100);
};

console.log('🚀 Sortable.js 拖拽管理器已加载');

// ====== 实时验证管理器 - Phase 2.4 ======

class ValidationManager {
    constructor(adjustmentManager, dragDropManager) {
        this.adjustmentManager = adjustmentManager;
        this.dragDropManager = dragDropManager;
        this.validationRules = [
            this.checkPrioritySequence.bind(this),
            this.checkHandlerCapacity.bind(this),
            this.checkTechnicalMatch.bind(this)
        ];
        this.lastValidationTime = 0;
        this.validationDelay = 1000; // 1秒防抖
        this.validationTimeout = null;
    }
    
    // 执行全面验证
    async performComprehensiveValidation(updates = null) {
        try {
            const updateData = updates || this.dragDropManager.collectUpdateData();
            
            if (updateData.length === 0) {
                return { valid: true, message: '没有需要验证的更改', warnings: [] };
            }
            
            const results = [];
            
            // 本地验证
            for (const rule of this.validationRules) {
                const result = await rule(updateData);
                results.push(result);
            }
            
            // API验证
            const apiResult = await this.dragDropManager.validateAdjustmentAPI(updateData);
            if (apiResult.validation_result) {
                results.push(...apiResult.validation_result);
            }
            
            // 合并验证结果
            return this.mergeValidationResults(results);
            
        } catch (error) {
            console.error('验证失败:', error);
            return {
                valid: false,
                message: `验证失败: ${error.message}`,
                errors: [error.message]
            };
        }
    }
    
    // 防抖验证
    debounceValidation(updates = null) {
        clearTimeout(this.validationTimeout);
        
        this.validationTimeout = setTimeout(async () => {
            const result = await this.performComprehensiveValidation(updates);
            this.displayValidationResult(result);
        }, this.validationDelay);
    }
    
    // 检查优先级序列
    async checkPrioritySequence(updates) {
        const warnings = [];
        const errors = [];
        
        // 按分选机分组检查优先级连续性
        const handlerGroups = {};
        updates.forEach(update => {
            const handlerId = update.handler_id;
            if (!handlerGroups[handlerId]) {
                handlerGroups[handlerId] = [];
            }
            handlerGroups[handlerId].push(update.priority);
        });
        
        Object.entries(handlerGroups).forEach(([handlerId, priorities]) => {
            priorities.sort((a, b) => a - b);
            for (let i = 0; i < priorities.length; i++) {
                if (priorities[i] !== i + 1) {
                    errors.push(`分选机 ${handlerId} 优先级序列不连续，期望第${i+1}位为${i+1}，实际为${priorities[i]}`);
                    break;
                }
            }
            
            // 检查是否有重复优先级
            const uniquePriorities = [...new Set(priorities)];
            if (uniquePriorities.length !== priorities.length) {
                errors.push(`分选机 ${handlerId} 存在重复优先级`);
            }
        });
        
        return { 
            type: 'priority', 
            level: errors.length > 0 ? 'error' : 'success',
            message: errors.length > 0 ? '优先级检查失败' : '优先级检查通过',
            warnings, 
            errors 
        };
    }
    
    // 检查分选机负载能力
    async checkHandlerCapacity(updates) {
        const warnings = [];
        const errors = [];
        
        // 计算每个分选机的负载
        const handlerLoads = {};
        const maxCapacity = 10000; // 每个分选机的最大容量
        
        updates.forEach(update => {
            const handlerId = update.handler_id;
            const quantity = update.quantity || 1000;
            
            if (handlerId && handlerId !== 'UNASSIGNED') {
                if (!handlerLoads[handlerId]) {
                    handlerLoads[handlerId] = 0;
                }
                handlerLoads[handlerId] += quantity;
            }
        });
        
        Object.entries(handlerLoads).forEach(([handlerId, load]) => {
            const loadPercentage = (load / maxCapacity) * 100;
            
            if (loadPercentage > 100) {
                errors.push(`分选机 ${handlerId} 负载超限: ${loadPercentage.toFixed(1)}% (${load.toLocaleString()}件)`);
            } else if (loadPercentage > 90) {
                warnings.push(`分选机 ${handlerId} 负载较高: ${loadPercentage.toFixed(1)}% (${load.toLocaleString()}件)`);
            } else if (loadPercentage < 20) {
                warnings.push(`分选机 ${handlerId} 负载较低: ${loadPercentage.toFixed(1)}% (${load.toLocaleString()}件)`);
            }
        });
        
        return { 
            type: 'capacity', 
            level: errors.length > 0 ? 'error' : warnings.length > 0 ? 'warning' : 'success',
            message: errors.length > 0 ? '负载检查失败' : warnings.length > 0 ? '负载检查有警告' : '负载检查通过',
            warnings, 
            errors,
            details: { handlerLoads, maxCapacity }
        };
    }
    
    // 检查技术匹配度
    async checkTechnicalMatch(updates) {
        const warnings = [];
        const errors = [];
        
        // 模拟技术匹配检查
        updates.forEach(update => {
            const lotId = update.lot_id;
            const handlerId = update.handler_id;
            
            if (handlerId && handlerId !== 'UNASSIGNED') {
                // 这里可以集成实际的匹配算法
                const matchScore = Math.floor(Math.random() * 40) + 60; // 模拟60-100分
                
                if (matchScore < 70) {
                    warnings.push(`批次 ${lotId} 与分选机 ${handlerId} 匹配度较低: ${matchScore}%`);
                } else if (matchScore > 95) {
                    // 高匹配度可以作为正面信息
                }
            }
        });
        
        return { 
            type: 'technical', 
            level: warnings.length > 0 ? 'warning' : 'success',
            message: warnings.length > 0 ? '技术匹配检查有警告' : '技术匹配检查通过',
            warnings, 
            errors 
        };
    }
    
    // 合并验证结果
    mergeValidationResults(results) {
        const merged = {
            valid: true,
            message: '',
            warnings: [],
            errors: [],
            details: {}
        };
        
        results.forEach(result => {
            if (result.errors && result.errors.length > 0) {
                merged.valid = false;
                merged.errors.push(...result.errors);
            }
            
            if (result.warnings && result.warnings.length > 0) {
                merged.warnings.push(...result.warnings);
            }
            
            if (result.details) {
                merged.details[result.type] = result.details;
            }
        });
        
        // 生成综合消息
        if (merged.errors.length > 0) {
            merged.message = `验证失败: ${merged.errors.length} 个错误`;
        } else if (merged.warnings.length > 0) {
            merged.message = `验证通过，但有 ${merged.warnings.length} 个警告`;
        } else {
            merged.message = '验证完全通过';
        }
        
        return merged;
    }
    
    // 显示验证结果
    displayValidationResult(result) {
        // 清除之前的验证提示
        this.clearValidationDisplay();
        
        if (!result.valid) {
            // 显示错误
            this.showValidationErrors(result.errors);
        } else if (result.warnings.length > 0) {
            // 显示警告
            this.showValidationWarnings(result.warnings);
        } else {
            // 显示成功
            this.showValidationSuccess(result.message);
        }
        
        console.log('🔍 验证结果:', result);
    }
    
    // 显示验证错误
    showValidationErrors(errors) {
        // 使用统一的通知系统而不是创建独立的警告框
        const errorMsg = `验证失败: ${errors.slice(0,2).join('; ')}${errors.length > 2 ? '...' : ''}`;
        showNotification('error', errorMsg);
        console.error('验证错误:', errors);
    }
    
    // 显示验证警告
    showValidationWarnings(warnings) {
        // 使用统一的通知系统
        const warningMsg = `验证警告: ${warnings.slice(0,2).join('; ')}${warnings.length > 2 ? '...' : ''}`;
        showNotification('warning', warningMsg);
        console.warn('验证警告:', warnings);
    }
    
    // 显示验证成功
    showValidationSuccess(message) {
        // 简单的控制台输出，避免过多的成功提示
        console.log('✅', message);
    }
    
    // 清除验证显示
    clearValidationDisplay() {
        // 清除标记的错误卡片
        document.querySelectorAll('.validation-error').forEach(card => {
            card.classList.remove('validation-error');
        });
    }
}

// 全局验证管理器实例
let validationManager = null;

// ====== 表格排序功能 ======
class TableSortManager {
    constructor(tableId) {
        this.table = document.getElementById(tableId);
        this.sortState = {}; // 存储每列的排序状态
        this.init();
    }
    
    init() {
        if (!this.table) return;
        
        // 为可排序的表头添加点击事件
        this.table.querySelectorAll('th.sortable').forEach(th => {
            th.addEventListener('click', (e) => {
                e.preventDefault();
                const column = th.dataset.column;
                this.toggleSort(column, th);
            });
        });
    }
    
    toggleSort(column, thElement) {
        // 获取当前排序状态
        const currentSort = this.sortState[column] || 'none';
        let newSort;
        
        // 切换排序状态: none -> asc -> desc -> none
        switch (currentSort) {
            case 'none':
                newSort = 'asc';
                break;
            case 'asc':
                newSort = 'desc';
                break;
            case 'desc':
                newSort = 'none';
                break;
        }
        
        // 清除其他列的排序状态
        this.clearAllSortStates();
        
        // 设置新的排序状态
        this.sortState[column] = newSort;
        this.updateSortIcon(thElement, newSort);
        
        // 执行排序
        if (newSort !== 'none') {
            this.sortTableData(column, newSort);
        } else {
            // 恢复原始顺序
            this.restoreOriginalOrder();
        }
    }
    
    clearAllSortStates() {
        // 清除所有表头的排序状态和图标
        this.table.querySelectorAll('th.sortable').forEach(th => {
            th.classList.remove('sorted-asc', 'sorted-desc');
            const icon = th.querySelector('.sort-icon');
            if (icon) {
                icon.className = 'fas fa-sort text-muted sort-icon';
            }
        });
        
        // 清除所有排序状态
        this.sortState = {};
    }
    
    updateSortIcon(thElement, sortState) {
        thElement.classList.remove('sorted-asc', 'sorted-desc');
        const icon = thElement.querySelector('.sort-icon');
        
        if (icon) {
            switch (sortState) {
                case 'asc':
                    thElement.classList.add('sorted-asc');
                    icon.className = 'fas fa-sort-up text-primary sort-icon';
                    break;
                case 'desc':
                    thElement.classList.add('sorted-desc');
                    icon.className = 'fas fa-sort-down text-primary sort-icon';
                    break;
                default:
                    icon.className = 'fas fa-sort text-muted sort-icon';
            }
        }
    }
    
    sortTableData(column, direction) {
        const tbody = this.table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        
        rows.sort((a, b) => {
            let aVal = this.getCellValue(a, column);
            let bVal = this.getCellValue(b, column);
            
            // 类型转换和比较
            if (this.isNumeric(aVal) && this.isNumeric(bVal)) {
                aVal = parseFloat(aVal);
                bVal = parseFloat(bVal);
            } else if (this.isDate(aVal) && this.isDate(bVal)) {
                aVal = new Date(aVal);
                bVal = new Date(bVal);
            } else {
                aVal = String(aVal).toLowerCase();
                bVal = String(bVal).toLowerCase();
            }
            
            if (aVal < bVal) return direction === 'asc' ? -1 : 1;
            if (aVal > bVal) return direction === 'asc' ? 1 : -1;
            return 0;
        });
        
        // 重新插入排序后的行
        rows.forEach(row => tbody.appendChild(row));
        
        console.log(`📊 表格按${column}列${direction === 'asc' ? '升序' : '降序'}排序完成`);
    }
    
    getCellValue(row, column) {
        const cellIndex = this.getColumnIndex(column);
        if (cellIndex === -1) return '';
        
        const cell = row.cells[cellIndex];
        if (!cell) return '';
        
        // 获取单元格的文本内容
        let value = cell.textContent.trim();
        
        // 特殊处理某些列
        switch (column) {
            case 'quantity':
                // 移除数量列的格式化字符
                value = value.replace(/[,件]/g, '');
                break;
            case 'score':
                // 从badge中提取分数
                const badge = cell.querySelector('.badge');
                if (badge) {
                    value = badge.textContent.trim();
                }
                break;
            case 'priority':
                // 优先级可能是数字
                value = value.replace(/[^0-9.]/g, '');
                break;
        }
        
        return value;
    }
    
    getColumnIndex(column) {
        const headers = this.table.querySelectorAll('th');
        for (let i = 0; i < headers.length; i++) {
            if (headers[i].dataset.column === column) {
                return i;
            }
        }
        return -1;
    }
    
    isNumeric(value) {
        return !isNaN(parseFloat(value)) && isFinite(value);
    }
    
    isDate(value) {
        return !isNaN(Date.parse(value));
    }
    
    restoreOriginalOrder() {
        // 重新加载数据以恢复原始顺序
        console.log('📊 恢复表格原始排序');
        // 这里可以调用重新渲染表格的函数
        renderTable();
    }
}

// ====== 列宽调整功能 ======
class ColumnResizeManager {
    constructor(tableId) {
        this.table = document.getElementById(tableId);
        this.isResizing = false;
        this.currentColumn = null;
        this.startX = 0;
        this.startWidth = 0;
        this.init();
    }
    
    init() {
        if (!this.table) return;
        
        // 为每个调整手柄添加事件
        this.table.querySelectorAll('.resize-handle').forEach(handle => {
            handle.addEventListener('mousedown', this.handleMouseDown.bind(this));
        });
        
        // 添加全局事件监听
        document.addEventListener('mousemove', this.handleMouseMove.bind(this));
        document.addEventListener('mouseup', this.handleMouseUp.bind(this));
    }
    
    handleMouseDown(e) {
        e.preventDefault();
        e.stopPropagation();
        
        this.isResizing = true;
        this.currentColumn = e.target.parentElement; // th元素
        this.startX = e.clientX;
        this.startWidth = this.currentColumn.offsetWidth;
        
        // 添加调整中的样式
        e.target.classList.add('resizing');
        document.body.style.cursor = 'col-resize';
        
        console.log('🔄 开始调整列宽');
    }
    
    handleMouseMove(e) {
        if (!this.isResizing || !this.currentColumn) return;
        
        e.preventDefault();
        
        const diff = e.clientX - this.startX;
        const newWidth = this.startWidth + diff;
        
        // 设置最小宽度
        const minWidth = 60;
        if (newWidth >= minWidth) {
            this.currentColumn.style.width = newWidth + 'px';
        }
    }
    
    handleMouseUp(e) {
        if (!this.isResizing) return;
        
        this.isResizing = false;
        document.body.style.cursor = '';
        
        // 移除调整中的样式
        document.querySelectorAll('.resize-handle.resizing').forEach(handle => {
            handle.classList.remove('resizing');
        });
        
        if (this.currentColumn) {
            console.log(`📊 列宽调整完成: ${this.currentColumn.style.width}`);
            this.currentColumn = null;
        }
    }
}

// 全局管理器实例
let tableSortManager = null;
let finalResultSortManager = null;  // 新增最终结果表格排序管理器
let columnResizeManager = null;
let finalResultColumnResizeManager = null;  // 新增最终结果表格列宽调整管理器

// 初始化表格增强功能
function initializeTableEnhancements() {
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            initTableFeatures();
        });
    } else {
        initTableFeatures();
    }
}

function initTableFeatures() {
    // 初始化查看/调整模式表格排序功能
    tableSortManager = new TableSortManager('dataTable');
    
    // 初始化最终结果表格排序功能
    finalResultSortManager = new TableSortManager('finalResultDataTable');
    
    // 初始化查看/调整模式表格列宽调整功能
    columnResizeManager = new ColumnResizeManager('dataTable');
    
    // 初始化最终结果表格列宽调整功能
    finalResultColumnResizeManager = new ColumnResizeManager('finalResultDataTable');
    
    console.log('📊 表格增强功能已初始化：查看模式排序 + 最终结果排序 + 双表格列宽调整');
}

// 在页面加载时初始化表格增强功能
initializeTableEnhancements();
</script>
{% endblock %} 