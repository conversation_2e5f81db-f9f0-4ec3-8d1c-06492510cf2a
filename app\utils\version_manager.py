#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本号管理工具模块
提供统一的版本号访问接口，确保整个应用的版本号一致性
"""

def get_app_version():
    """
    获取应用版本号
    
    Returns:
        str: 应用版本号（格式：v*******）
    """
    try:
        # 🏷️ 优先从统一版本号管理中心获取
        from app.services import APP_VERSION
        return APP_VERSION
    except ImportError:
        # 降级方案：返回默认版本号
        return '2.3.4'

def get_version_info():
    """
    获取详细版本信息
    
    Returns:
        dict: 版本信息字典
    """
    try:
        # 🏷️ 优先从统一版本号管理中心获取
        from app.services import VERSION_INFO
        return VERSION_INFO
    except ImportError:
        # 降级方案：返回默认版本信息
        return {
            'version': '2.3.4',
            'app_version': '2.3.4',
            'build_date': '2025-01-16',
            'description': 'AEC-FT智能排产指挥平台'
        }

def get_raw_version():
    """
    获取原始版本号（不带v前缀）
    
    Returns:
        str: 原始版本号（格式：*******）
    """
    try:
        # 🏷️ 优先从统一版本号管理中心获取
        from app.services import __version__
        return __version__
    except ImportError:
        # 降级方案：返回默认版本号
        return '2.3.4'

def validate_version_consistency():
    """
    验证版本号一致性
    
    Returns:
        dict: 验证结果
    """
    results = {
        'consistent': True,
        'issues': [],
        'versions': {}
    }
    
    try:
        # 检查各模块的版本号
        from app.services import __version__, APP_VERSION, VERSION_INFO
        results['versions']['services'] = {
            'raw': __version__,
            'app': APP_VERSION,
            'info': VERSION_INFO
        }
        
        # 检查配置中的版本号
        from config.aps_config import config
        results['versions']['config'] = config.APP_VERSION
        
        # 检查版本号一致性
        if APP_VERSION != config.APP_VERSION:
            results['consistent'] = False
            results['issues'].append(f"services.APP_VERSION ({APP_VERSION}) != config.APP_VERSION ({config.APP_VERSION})")
        
        if f"v{__version__}" != APP_VERSION:
            results['consistent'] = False
            results['issues'].append(f"版本前缀不一致: v{__version__} != {APP_VERSION}")
            
    except ImportError as e:
        results['consistent'] = False
        results['issues'].append(f"导入错误: {e}")
    
    return results

if __name__ == '__main__':
    """测试版本号管理功能"""
    print("🏷️ APS平台版本号管理测试")
    print("=" * 50)
    
    print("📋 版本信息:")
    print(f"  应用版本号: {get_app_version()}")
    print(f"  原始版本号: {get_raw_version()}")
    
    print("\n📊 详细版本信息:")
    version_info = get_version_info()
    for key, value in version_info.items():
        print(f"  {key}: {value}")
    
    print("\n🔍 版本号一致性检查:")
    validation = validate_version_consistency()
    if validation['consistent']:
        print("  ✅ 版本号一致性检查通过")
    else:
        print("  ❌ 版本号一致性检查失败:")
        for issue in validation['issues']:
            print(f"    - {issue}")
    
    print("\n📁 各模块版本号:")
    for module, version in validation['versions'].items():
        print(f"  {module}: {version}")
