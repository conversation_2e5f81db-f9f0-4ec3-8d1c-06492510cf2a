#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查JWH7030QFNAZ产品的排产结果
"""

import pymysql
import json

def check_jwh7030_results():
    """检查JWH7030QFNAZ产品的排产结果"""
    
    # 连接数据库
    connection = pymysql.connect(
        host='localhost',
        user='root', 
        password='WWWwww123!',
        database='aps',
        charset='utf8mb4'
    )

    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            print("🎯 检查JWH7030QFNAZ产品的排产结果...")
            
            # 查询排产结果表中的JWH7030QFNAZ产品
            cursor.execute("""
                SELECT LOT_ID, DEVICE, STAGE, HANDLER_ID, COMPREHENSIVE_SCORE,
                       MATCH_TYPE, CHANGEOVER_TIME, PROCESSING_TIME, CREATE_TIME
                FROM lotprioritydone
                WHERE DEVICE LIKE %s
                ORDER BY CREATE_TIME DESC
            """, ('%JWH7030QFNAZ%',))
            
            results = cursor.fetchall()
            
            if results:
                print(f"\n✅ 找到 {len(results)} 个JWH7030QFNAZ产品的排产结果!")
                print("=" * 100)
                
                for i, result in enumerate(results, 1):
                    print(f"\n【批次 {i}】")
                    print(f"  批次ID: {result['LOT_ID']}")
                    print(f"  产品: {result['DEVICE']}")
                    print(f"  工序: {result['STAGE']}")
                    print(f"  分配设备: {result['HANDLER_ID']}")
                    print(f"  综合评分: {result['COMPREHENSIVE_SCORE']}")
                    print(f"  匹配类型: {result['MATCH_TYPE']}")
                    print(f"  改机时间: {result['CHANGEOVER_TIME']}分钟")
                    print(f"  处理时间: {result['PROCESSING_TIME']}小时")
                    print(f"  排产时间: {result['CREATE_TIME']}")
                    print("  " + "-" * 80)
                
                # 统计分析
                print(f"\n📈 排产效果分析:")
                print("=" * 60)
                
                # 按设备统计
                device_stats = {}
                for result in results:
                    device = result['HANDLER_ID']
                    if device not in device_stats:
                        device_stats[device] = []
                    device_stats[device].append(result)
                
                print(f"涉及设备数量: {len(device_stats)}")
                for device, batches in device_stats.items():
                    print(f"  {device}: {len(batches)} 个批次")
                
                # 按工序统计
                stage_stats = {}
                for result in results:
                    stage = result['STAGE']
                    if stage not in stage_stats:
                        stage_stats[stage] = 0
                    stage_stats[stage] += 1
                
                print(f"\n按工序分布:")
                for stage, count in stage_stats.items():
                    print(f"  {stage}: {count} 个批次")
                
                # 评分统计
                scores = [float(r['COMPREHENSIVE_SCORE']) for r in results if r['COMPREHENSIVE_SCORE']]
                if scores:
                    avg_score = sum(scores) / len(scores)
                    max_score = max(scores)
                    min_score = min(scores)
                    print(f"\n评分统计:")
                    print(f"  平均评分: {avg_score:.2f}")
                    print(f"  最高评分: {max_score:.2f}")
                    print(f"  最低评分: {min_score:.2f}")
                
                # 检查是否有批次分配到了IDLE状态的专用设备
                idle_device_assigned = False
                for result in results:
                    if result['HANDLER_ID'] == 'HCHC-O-004-6800-ROOM-TEST':
                        idle_device_assigned = True
                        print(f"\n🏆 成功！批次 {result['LOT_ID']} 被分配到IDLE状态的专用设备!")
                        print(f"   综合评分: {result['COMPREHENSIVE_SCORE']}")
                        print(f"   匹配类型: {result['MATCH_TYPE']}")
                        break
                
                if not idle_device_assigned:
                    print(f"\n⚠️ 注意：没有批次被分配到IDLE状态的专用设备 HCHC-O-004-6800-ROOM-TEST")
                
                print(f"\n✅ JWH7030QFNAZ产品排产优化验证完成!")
                print(f"✅ 成功解决了'无合适设备'的问题，{len(results)}个批次成功排产!")
                
            else:
                print("❌ 没有找到JWH7030QFNAZ产品的排产结果")
                
                # 检查是否还有待排产的批次
                cursor.execute("""
                    SELECT LOT_ID, DEVICE, STAGE, GOOD_QTY
                    FROM et_wait_lot 
                    WHERE DEVICE LIKE %s
                """, ('%JWH7030QFNAZ%',))
                
                wait_results = cursor.fetchall()
                if wait_results:
                    print(f"\n🔍 仍有 {len(wait_results)} 个JWH7030QFNAZ批次在等待排产:")
                    for result in wait_results:
                        print(f"  批次: {result['LOT_ID']}, 工序: {result['STAGE']}, 数量: {result['GOOD_QTY']}")

    finally:
        connection.close()

if __name__ == '__main__':
    check_jwh7030_results()
