#!/usr/bin/env python3
"""
清理失败批次页面缓存的脚本
解决缓存导致的JavaScript不执行问题
"""

import os
import time
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clear_browser_cache_headers():
    """修改缓存头部设置，强制浏览器重新加载"""
    try:
        logger.info("🔧 修改缓存头部设置...")
        
        cache_optimizer_path = "app/utils/cache_optimizer.py"
        if os.path.exists(cache_optimizer_path):
            with open(cache_optimizer_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 临时禁用HTML页面缓存
            old_html_cache = """# HTML页面缓存
            elif response.content_type and 'text/html' in response.content_type:
                response.cache_control.max_age = 300  # 5分钟
                response.cache_control.must_revalidate = True"""
            
            new_html_cache = """# HTML页面缓存 - 临时禁用缓存解决失败批次页面问题
            elif response.content_type and 'text/html' in response.content_type:
                response.cache_control.no_cache = True
                response.cache_control.no_store = True
                response.cache_control.must_revalidate = True
                response.headers['Pragma'] = 'no-cache'
                response.headers['Expires'] = '0'"""
            
            if old_html_cache.replace(' ', '').replace('\n', '') in content.replace(' ', '').replace('\n', ''):
                content = content.replace(
                    "# HTML页面缓存\n            elif response.content_type and 'text/html' in response.content_type:\n                response.cache_control.max_age = 300  # 5分钟\n                response.cache_control.must_revalidate = True",
                    new_html_cache
                )
                
                with open(cache_optimizer_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.info("✅ 已修改HTML页面缓存设置")
            else:
                logger.info("⚠️ 未找到需要修改的缓存设置")
        else:
            logger.warning("⚠️ 缓存优化器文件不存在")
            
    except Exception as e:
        logger.error(f"❌ 修改缓存设置失败: {e}")

def add_cache_busting_to_template():
    """为失败批次模板添加缓存破坏参数"""
    try:
        logger.info("🔧 为模板添加缓存破坏参数...")
        
        template_path = "app/templates/production/failed_lots.html"
        if os.path.exists(template_path):
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 添加缓存破坏参数到页面头部
            cache_buster = f"<!-- Cache Buster: {int(time.time())} -->\n"
            
            if "<!-- Cache Buster:" not in content:
                # 在<head>标签后添加缓存破坏注释
                content = content.replace(
                    "{% block title %}{{ page_title }} - AEC-FT ICP{% endblock %}",
                    "{% block title %}{{ page_title }} - AEC-FT ICP{% endblock %}\n\n" + cache_buster
                )
                
                with open(template_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.info("✅ 已添加缓存破坏参数")
            else:
                logger.info("⚠️ 缓存破坏参数已存在")
                
        else:
            logger.warning("⚠️ 失败批次模板文件不存在")
            
    except Exception as e:
        logger.error(f"❌ 添加缓存破坏参数失败: {e}")

def create_cache_clear_endpoint():
    """创建缓存清理API端点"""
    try:
        logger.info("🔧 创建缓存清理API端点...")
        
        # 检查是否已存在缓存清理端点
        api_file_path = "app/api_v2/system/cache_api.py"
        
        if not os.path.exists(api_file_path):
            os.makedirs(os.path.dirname(api_file_path), exist_ok=True)
            
            cache_api_content = '''#!/usr/bin/env python3
"""
缓存管理API - 解决失败批次页面缓存问题
"""

from flask import Blueprint, jsonify, request
from flask_login import login_required
import logging

logger = logging.getLogger(__name__)

cache_bp = Blueprint('cache', __name__)

@cache_bp.route('/clear-page-cache', methods=['POST'])
@login_required
def clear_page_cache():
    """清理页面缓存"""
    try:
        page_name = request.json.get('page_name', 'all')
        
        # 这里可以添加具体的缓存清理逻辑
        # 例如清理Redis缓存、内存缓存等
        
        logger.info(f"🧹 清理页面缓存: {page_name}")
        
        return jsonify({
            'success': True,
            'message': f'页面缓存已清理: {page_name}',
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"清理页面缓存失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@cache_bp.route('/force-refresh', methods=['POST'])
@login_required
def force_refresh():
    """强制刷新指定页面"""
    try:
        page_url = request.json.get('page_url', '')
        
        # 返回强制刷新指令
        return jsonify({
            'success': True,
            'action': 'force_refresh',
            'page_url': page_url,
            'cache_buster': int(time.time()),
            'message': '请刷新页面以获取最新内容'
        })
        
    except Exception as e:
        logger.error(f"强制刷新失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
'''
            
            with open(api_file_path, 'w', encoding='utf-8') as f:
                f.write(cache_api_content)
            
            logger.info("✅ 已创建缓存清理API端点")
        else:
            logger.info("⚠️ 缓存清理API端点已存在")
            
    except Exception as e:
        logger.error(f"❌ 创建缓存清理API端点失败: {e}")

def generate_cache_fix_instructions():
    """生成缓存修复说明"""
    instructions = f"""
# 失败批次页面缓存问题修复说明

## 🔍 问题原因
失败批次页面的JavaScript没有执行，是因为浏览器缓存了旧版本的页面。

## 🔧 修复措施
1. 已修改HTML页面缓存设置，禁用缓存
2. 已为模板添加缓存破坏参数
3. 已创建缓存清理API端点

## 🚀 用户解决方案

### 方案1：强制刷新（推荐）
1. 在失败批次页面按 `Ctrl + F5` 强制刷新
2. 或按 `F12` 打开开发者工具，右键刷新按钮选择"清空缓存并硬性重新加载"

### 方案2：清除浏览器缓存
1. 按 `Ctrl + Shift + Delete` 打开清除浏览器数据
2. 选择"缓存的图片和文件"
3. 点击"清除数据"

### 方案3：使用无痕模式
1. 按 `Ctrl + Shift + N` 打开无痕窗口
2. 访问失败批次页面

## 📊 验证方法
刷新后应该在控制台看到：
```
🔥 失败批次页面初始化...
📊 页面URL: http://localhost:5000/production/failed-lots
📊 页面标题: 排产失败清单 - AEC-FT ICP
✅ 所有必要的DOM元素都存在
✅ 事件监听器初始化完成
✅ 排序功能初始化完成
🔍 开始加载失败批次数据...
```

## 🎯 修复时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📞 如果问题仍然存在
请提供浏览器控制台的完整日志信息。
"""
    
    with open('failed_lots_cache_fix_instructions.md', 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    logger.info("📄 已生成缓存修复说明文件")

def main():
    """主函数"""
    logger.info("🚀 开始修复失败批次页面缓存问题...")
    logger.info("=" * 60)
    
    try:
        # 1. 修改缓存头部设置
        clear_browser_cache_headers()
        
        # 2. 为模板添加缓存破坏参数
        add_cache_busting_to_template()
        
        # 3. 创建缓存清理API端点
        create_cache_clear_endpoint()
        
        # 4. 生成修复说明
        generate_cache_fix_instructions()
        
        logger.info("=" * 60)
        logger.info("🎉 缓存问题修复完成！")
        logger.info("💡 用户需要强制刷新页面（Ctrl + F5）以获取最新内容")
        logger.info("📄 详细说明请查看 failed_lots_cache_fix_instructions.md")
        
    except Exception as e:
        logger.error(f"❌ 修复过程中出错: {e}")

if __name__ == "__main__":
    main()
