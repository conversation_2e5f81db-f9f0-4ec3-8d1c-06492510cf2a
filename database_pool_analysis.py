#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接池性能衰减分析和优化
解决长时间运行后系统卡顿问题
"""

import time
import threading
from datetime import datetime, timedelta
import json

def analyze_current_pool_config():
    """分析当前连接池配置"""
    print("🔍 数据库连接池配置分析")
    print("=" * 60)
    
    try:
        from app.utils.db_connection_pool import get_connection_pool
        from config.aps_config import config
        
        pool = get_connection_pool()
        
        print("📊 当前连接池配置:")
        print(f"   最小连接数: {pool._min_pool_size}")
        print(f"   初始连接数: {pool._pool_size}")
        print(f"   最大连接数: {pool._max_connections}")
        print(f"   连接超时: {pool._connection_timeout}秒")
        print(f"   池获取超时: {pool._pool_timeout}秒")
        print(f"   连接最大存活时间: {pool._connection_max_age}秒 ({pool._connection_max_age/3600:.1f}小时)")
        print(f"   空闲连接超时: {pool._idle_timeout}秒 ({pool._idle_timeout/60:.1f}分钟)")
        print(f"   健康检查间隔: {pool._health_check_interval}秒 ({pool._health_check_interval/60:.1f}分钟)")
        
        print("\n🎯 配置评估:")
        
        # 评估配置合理性
        issues = []
        recommendations = []
        
        # 1. 连接数配置
        if pool._max_connections > 50:
            issues.append("最大连接数过高，可能导致数据库压力")
            recommendations.append("建议最大连接数设置为20-30")
        
        # 2. 连接存活时间
        if pool._connection_max_age > 7200:  # 2小时
            issues.append("连接存活时间过长，可能导致连接失效")
            recommendations.append("建议连接最大存活时间设置为1-2小时")
        
        # 3. 健康检查频率
        if pool._health_check_interval > 300:  # 5分钟
            issues.append("健康检查间隔过长，无法及时发现问题连接")
            recommendations.append("建议健康检查间隔设置为1-3分钟")
        
        # 4. 空闲超时
        if pool._idle_timeout > 600:  # 10分钟
            issues.append("空闲连接超时过长，可能占用资源")
            recommendations.append("建议空闲连接超时设置为5-10分钟")
        
        if issues:
            print("   ⚠️ 发现的问题:")
            for issue in issues:
                print(f"     - {issue}")
            
            print("\n   💡 优化建议:")
            for rec in recommendations:
                print(f"     - {rec}")
        else:
            print("   ✅ 配置基本合理")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置分析失败: {e}")
        return False

def test_connection_pool_performance():
    """测试连接池性能"""
    print("\n🧪 连接池性能测试")
    print("=" * 60)
    
    try:
        from app.utils.db_connection_pool import get_connection_pool
        
        pool = get_connection_pool()
        
        # 测试1: 连接获取速度
        print("📊 测试1: 连接获取速度")
        times = []
        for i in range(10):
            start_time = time.time()
            try:
                conn = pool.get_connection()
                end_time = time.time()
                pool.return_connection(conn)
                times.append((end_time - start_time) * 1000)
            except Exception as e:
                print(f"   连接获取失败: {e}")
                break
        
        if times:
            avg_time = sum(times) / len(times)
            min_time = min(times)
            max_time = max(times)
            print(f"   平均获取时间: {avg_time:.1f}ms")
            print(f"   最快获取时间: {min_time:.1f}ms")
            print(f"   最慢获取时间: {max_time:.1f}ms")
            
            if avg_time > 100:
                print("   ⚠️ 连接获取速度较慢")
            else:
                print("   ✅ 连接获取速度正常")
        
        # 测试2: 并发连接测试
        print("\n📊 测试2: 并发连接测试")
        concurrent_results = []
        
        def concurrent_test():
            try:
                start_time = time.time()
                conn = pool.get_connection()
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                cursor.close()
                pool.return_connection(conn)
                end_time = time.time()
                concurrent_results.append((end_time - start_time) * 1000)
            except Exception as e:
                concurrent_results.append(None)
        
        threads = []
        for i in range(5):
            thread = threading.Thread(target=concurrent_test)
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        successful_times = [t for t in concurrent_results if t is not None]
        if successful_times:
            avg_concurrent = sum(successful_times) / len(successful_times)
            print(f"   并发平均时间: {avg_concurrent:.1f}ms")
            print(f"   成功率: {len(successful_times)}/{len(concurrent_results)}")
            
            if avg_concurrent > 200:
                print("   ⚠️ 并发性能较差")
            else:
                print("   ✅ 并发性能正常")
        
        # 测试3: 连接池状态
        print("\n📊 测试3: 连接池状态")
        stats = pool.get_pool_stats()
        print(f"   总连接数: {stats.get('total_connections', 0)}")
        print(f"   活跃连接数: {stats.get('active_connections', 0)}")
        print(f"   连接复用次数: {stats.get('connections_reused', 0)}")
        print(f"   连接创建次数: {stats.get('connections_created', 0)}")
        print(f"   连接错误次数: {stats.get('connection_errors', 0)}")
        print(f"   健康检查次数: {stats.get('health_checks_performed', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def create_optimized_pool_config():
    """创建优化的连接池配置"""
    print("\n🔧 创建优化的连接池配置")
    print("=" * 60)
    
    optimized_config = """
# 优化的数据库连接池配置
# 解决长时间运行性能衰减问题

[DATABASE]
# 基础连接配置
host = localhost
port = 3306
database = aps
username = root
password = WWWwww123!
charset = utf8mb4

# 连接池优化配置
pool_size = 8           # 基础连接数 (降低资源占用)
max_overflow = 12       # 溢出连接数 (总计20个连接)
pool_timeout = 10       # 获取连接超时 (10秒)
pool_recycle = 3600     # 连接回收时间 (1小时，防止连接失效)

# 连接超时配置
connect_timeout = 10    # 连接超时
read_timeout = 30       # 读取超时
write_timeout = 30      # 写入超时

# 自定义连接池配置
custom_pool_min_size = 2    # 最小连接数
custom_pool_init_size = 4   # 初始连接数
custom_pool_max_size = 20   # 最大连接数

# 健康检查配置
health_check_interval = 180     # 健康检查间隔 (3分钟)
connection_max_age = 3600       # 连接最大存活时间 (1小时)
idle_timeout = 600              # 空闲连接超时 (10分钟)

# 监控和告警配置
monitor_enabled = true          # 启用监控
alert_threshold = 0.8           # 80%使用率告警
scale_up_threshold = 0.7        # 70%使用率时扩容
scale_down_threshold = 0.3      # 30%使用率时缩容

[APPLICATION]
# 应用配置
debug = false
host = 0.0.0.0
port = 5000
secret_key = your-secret-key-here

# 性能优化配置
templates_auto_reload = false
send_file_max_age_default = 31536000
permanent_session_lifetime = 28800

[LOGGING]
# 日志配置
level = INFO
file = logs/app.log
max_size = 10MB
backup_count = 5
"""
    
    config_path = "config_optimized.ini"
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(optimized_config)
    
    print(f"✅ 优化配置已保存到: {config_path}")
    
    print("\n🎯 优化要点:")
    print("1. 降低连接池大小，减少资源占用")
    print("2. 缩短连接存活时间，防止连接失效")
    print("3. 增加健康检查频率，及时发现问题")
    print("4. 启用动态扩缩容，适应负载变化")
    print("5. 优化超时配置，提高响应性")
    
    return config_path

def create_long_running_test():
    """创建长时间运行测试脚本"""
    print("\n🕐 创建长时间运行测试脚本")
    print("=" * 60)
    
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
长时间运行稳定性测试
模拟24小时以上的运行场景
"""

import time
import threading
import logging
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.FileHandler('long_running_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('LongRunningTest')

class LongRunningTester:
    def __init__(self):
        self.start_time = datetime.now()
        self.test_count = 0
        self.error_count = 0
        self.running = True
        
    def database_stress_test(self):
        """数据库压力测试"""
        while self.running:
            try:
                from app.utils.db_connection_pool import get_connection_pool
                
                pool = get_connection_pool()
                
                # 获取连接
                start_time = time.time()
                conn = pool.get_connection()
                cursor = conn.cursor()
                
                # 执行查询
                cursor.execute("SELECT COUNT(*) FROM et_wait_lot LIMIT 1")
                result = cursor.fetchone()
                
                # 清理
                cursor.close()
                pool.return_connection(conn)
                
                response_time = (time.time() - start_time) * 1000
                self.test_count += 1
                
                if response_time > 1000:  # 超过1秒
                    logger.warning(f"慢查询检测: {response_time:.1f}ms")
                
                if self.test_count % 100 == 0:
                    logger.info(f"已完成 {self.test_count} 次测试，平均响应时间: {response_time:.1f}ms")
                
                time.sleep(5)  # 每5秒测试一次
                
            except Exception as e:
                self.error_count += 1
                logger.error(f"数据库测试失败: {e}")
                time.sleep(10)  # 错误时等待更长时间
    
    def memory_monitor(self):
        """内存监控"""
        while self.running:
            try:
                import psutil
                import os
                
                process = psutil.Process(os.getpid())
                memory_mb = process.memory_info().rss / 1024 / 1024
                cpu_percent = process.cpu_percent()
                
                if memory_mb > 500:  # 超过500MB
                    logger.warning(f"内存使用过高: {memory_mb:.1f}MB")
                
                if cpu_percent > 80:  # 超过80%
                    logger.warning(f"CPU使用过高: {cpu_percent:.1f}%")
                
                logger.info(f"系统资源: 内存 {memory_mb:.1f}MB, CPU {cpu_percent:.1f}%")
                
                time.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                logger.error(f"内存监控失败: {e}")
                time.sleep(60)
    
    def connection_pool_monitor(self):
        """连接池监控"""
        while self.running:
            try:
                from app.utils.db_connection_pool import get_connection_pool
                
                pool = get_connection_pool()
                stats = pool.get_pool_stats()
                
                logger.info(f"连接池状态: 总连接 {stats.get('total_connections', 0)}, "
                           f"活跃连接 {stats.get('active_connections', 0)}, "
                           f"错误次数 {stats.get('connection_errors', 0)}")
                
                # 检查连接池健康状态
                total = stats.get('total_connections', 0)
                active = stats.get('active_connections', 0)
                errors = stats.get('connection_errors', 0)
                
                if total > 0:
                    usage_rate = active / total
                    if usage_rate > 0.9:
                        logger.warning(f"连接池使用率过高: {usage_rate:.1%}")
                
                if errors > self.test_count * 0.1:  # 错误率超过10%
                    logger.error(f"连接池错误率过高: {errors}/{self.test_count}")
                
                time.sleep(300)  # 每5分钟检查一次
                
            except Exception as e:
                logger.error(f"连接池监控失败: {e}")
                time.sleep(300)
    
    def run_test(self, duration_hours=24):
        """运行长时间测试"""
        logger.info(f"开始长时间运行测试，持续时间: {duration_hours}小时")
        
        # 启动监控线程
        threads = [
            threading.Thread(target=self.database_stress_test, daemon=True),
            threading.Thread(target=self.memory_monitor, daemon=True),
            threading.Thread(target=self.connection_pool_monitor, daemon=True)
        ]
        
        for thread in threads:
            thread.start()
        
        # 运行指定时间
        end_time = self.start_time + timedelta(hours=duration_hours)
        
        try:
            while datetime.now() < end_time and self.running:
                elapsed = datetime.now() - self.start_time
                remaining = end_time - datetime.now()
                
                logger.info(f"测试进行中... 已运行: {elapsed}, 剩余: {remaining}")
                logger.info(f"测试统计: 总测试 {self.test_count}, 错误 {self.error_count}")
                
                time.sleep(3600)  # 每小时报告一次
                
        except KeyboardInterrupt:
            logger.info("用户中断测试")
        
        self.running = False
        
        # 生成测试报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        elapsed = datetime.now() - self.start_time
        
        report = f"""
长时间运行测试报告
==================
开始时间: {self.start_time}
结束时间: {datetime.now()}
运行时长: {elapsed}
总测试次数: {self.test_count}
错误次数: {self.error_count}
成功率: {((self.test_count - self.error_count) / self.test_count * 100):.2f}%

结论:
{'✅ 系统稳定性良好' if self.error_count < self.test_count * 0.05 else '⚠️ 系统存在稳定性问题'}
"""
        
        logger.info(report)
        
        with open('long_running_test_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)

if __name__ == "__main__":
    tester = LongRunningTester()
    tester.run_test(24)  # 运行24小时
'''
    
    script_path = "long_running_test.py"
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print(f"✅ 长时间运行测试脚本已保存到: {script_path}")
    print("\n使用方法:")
    print("1. python long_running_test.py  # 运行24小时测试")
    print("2. 查看日志: long_running_test.log")
    print("3. 查看报告: long_running_test_report.txt")
    
    return script_path

def main():
    """主函数"""
    print("🔍 数据库连接池性能衰减问题分析")
    print("解决长时间运行后系统卡顿问题")
    print("=" * 60)
    
    # 1. 分析当前配置
    if not analyze_current_pool_config():
        print("❌ 无法分析当前配置，请确保应用已启动")
        return
    
    # 2. 性能测试
    if not test_connection_pool_performance():
        print("❌ 性能测试失败")
        return
    
    # 3. 创建优化配置
    config_path = create_optimized_pool_config()
    
    # 4. 创建长时间运行测试
    test_script = create_long_running_test()
    
    print("\n🎯 总结和建议:")
    print("1. 使用优化配置替换现有config.ini")
    print("2. 运行长时间测试验证稳定性")
    print("3. 监控连接池状态和系统资源")
    print("4. 定期重启应用（建议每周一次）")
    
    print(f"\n📁 生成的文件:")
    print(f"   - {config_path} (优化配置)")
    print(f"   - {test_script} (长时间运行测试)")

if __name__ == "__main__":
    main()
