#!/usr/bin/env python3
"""
诊断失败批次页面问题的综合脚本
检查路由、模板、API、数据库等各个环节
"""

import requests
import mysql.connector
from mysql.connector import Error
import json
import logging
from datetime import datetime
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 配置
BASE_URL = "http://localhost:5000"
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'WWWwww123!',
    'database': 'aps',
    'charset': 'utf8mb4'
}

def test_server_status():
    """测试服务器状态"""
    try:
        logger.info("🔍 1. 检查服务器状态...")
        response = requests.get(f"{BASE_URL}/", timeout=10)
        if response.status_code == 200:
            logger.info("✅ 服务器运行正常")
            return True
        else:
            logger.error(f"❌ 服务器状态异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ 无法连接到服务器: {e}")
        return False

def test_failed_lots_route():
    """测试失败批次页面路由"""
    try:
        logger.info("🔍 2. 测试失败批次页面路由...")
        response = requests.get(f"{BASE_URL}/production/failed-lots", timeout=30)
        logger.info(f"📡 页面响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            logger.info("✅ 失败批次页面路由正常")
            # 检查页面内容
            content = response.text
            if "失败批次详情" in content:
                logger.info("✅ 页面内容包含预期标题")
            else:
                logger.warning("⚠️ 页面内容可能不完整")
            
            if "loadFailedLots" in content:
                logger.info("✅ 页面包含JavaScript函数")
            else:
                logger.warning("⚠️ 页面缺少关键JavaScript函数")
                
            return True
        else:
            logger.error(f"❌ 页面路由失败，状态码: {response.status_code}")
            logger.error(f"响应内容: {response.text[:500]}...")
            return False
    except Exception as e:
        logger.error(f"❌ 测试页面路由失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    try:
        logger.info("🔍 3. 测试API端点...")
        
        # 测试当前失败批次API
        current_url = f"{BASE_URL}/api/v2/production/get-failed-lots-from-logs?current_only=true"
        response = requests.get(current_url, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                failed_lots = data.get('data', {}).get('failed_lots', [])
                logger.info(f"✅ 当前失败批次API正常，返回 {len(failed_lots)} 条记录")
            else:
                logger.error(f"❌ API返回失败: {data.get('message', '未知错误')}")
                return False
        else:
            logger.error(f"❌ API调用失败，状态码: {response.status_code}")
            return False
        
        # 测试历史失败批次API
        history_url = f"{BASE_URL}/api/v2/production/get-failed-lots-from-logs?current_only=false"
        response = requests.get(history_url, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                failed_lots = data.get('data', {}).get('failed_lots', [])
                logger.info(f"✅ 历史失败批次API正常，返回 {len(failed_lots)} 条记录")
            else:
                logger.error(f"❌ 历史API返回失败: {data.get('message', '未知错误')}")
                return False
        else:
            logger.error(f"❌ 历史API调用失败，状态码: {response.status_code}")
            return False
            
        return True
    except Exception as e:
        logger.error(f"❌ 测试API端点失败: {e}")
        return False

def test_database_status():
    """测试数据库状态"""
    try:
        logger.info("🔍 4. 测试数据库状态...")
        
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 检查表存在性
        cursor.execute("""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
            AND table_name = 'scheduling_failed_lots'
        """)
        
        table_exists = cursor.fetchone()[0] > 0
        if table_exists:
            logger.info("✅ scheduling_failed_lots表存在")
            
            # 检查记录数
            cursor.execute("SELECT COUNT(*) FROM scheduling_failed_lots")
            total_count = cursor.fetchone()[0]
            logger.info(f"📊 表中总记录数: {total_count}")
            
            # 检查最近记录
            cursor.execute("""
                SELECT COUNT(*) FROM scheduling_failed_lots 
                WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            """)
            recent_count = cursor.fetchone()[0]
            logger.info(f"📊 最近24小时记录数: {recent_count}")
            
            if total_count > 0:
                logger.info("✅ 数据库中有失败批次数据")
            else:
                logger.warning("⚠️ 数据库中没有失败批次数据")
                
        else:
            logger.error("❌ scheduling_failed_lots表不存在")
            return False
            
        cursor.close()
        connection.close()
        return True
        
    except Error as e:
        logger.error(f"❌ 数据库测试失败: {e}")
        return False

def test_template_files():
    """测试模板文件"""
    try:
        logger.info("🔍 5. 检查模板文件...")
        
        template_path = "app/templates/production/failed_lots.html"
        if os.path.exists(template_path):
            logger.info("✅ 失败批次模板文件存在")
            
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查关键内容
            key_elements = [
                'loadFailedLots',
                'renderFailedLots',
                'failedLotsTableBody',
                'get-failed-lots-from-logs'
            ]
            
            missing_elements = []
            for element in key_elements:
                if element not in content:
                    missing_elements.append(element)
            
            if missing_elements:
                logger.warning(f"⚠️ 模板文件缺少关键元素: {missing_elements}")
            else:
                logger.info("✅ 模板文件包含所有关键元素")
                
            return True
        else:
            logger.error("❌ 失败批次模板文件不存在")
            return False
            
    except Exception as e:
        logger.error(f"❌ 检查模板文件失败: {e}")
        return False

def generate_diagnosis_report():
    """生成诊断报告"""
    logger.info("🔍 6. 生成诊断报告...")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'tests': {
            'server_status': test_server_status(),
            'route_access': test_failed_lots_route(),
            'api_endpoints': test_api_endpoints(),
            'database_status': test_database_status(),
            'template_files': test_template_files()
        }
    }
    
    # 计算总体状态
    all_passed = all(report['tests'].values())
    
    logger.info("=" * 60)
    logger.info("📋 诊断报告摘要:")
    logger.info("=" * 60)
    
    for test_name, result in report['tests'].items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info("=" * 60)
    
    if all_passed:
        logger.info("🎉 所有测试通过！失败批次功能应该正常工作。")
        logger.info("💡 如果用户仍然看不到数据，可能是浏览器缓存问题，建议清除缓存或使用无痕模式。")
    else:
        logger.error("❌ 发现问题！请检查失败的测试项目。")
        
        # 提供解决建议
        if not report['tests']['server_status']:
            logger.info("💡 建议：请确保Flask应用正在运行")
        if not report['tests']['database_status']:
            logger.info("💡 建议：请检查数据库连接和表结构")
        if not report['tests']['api_endpoints']:
            logger.info("💡 建议：请检查API路由和权限配置")
        if not report['tests']['template_files']:
            logger.info("💡 建议：请检查模板文件完整性")
    
    return report

def main():
    """主函数"""
    logger.info("🚀 开始诊断失败批次页面问题...")
    logger.info("=" * 60)
    
    try:
        report = generate_diagnosis_report()
        
        # 保存报告到文件
        with open('failed_lots_diagnosis_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info("📄 诊断报告已保存到 failed_lots_diagnosis_report.json")
        
    except Exception as e:
        logger.error(f"❌ 诊断过程中出错: {e}")

if __name__ == "__main__":
    main()
