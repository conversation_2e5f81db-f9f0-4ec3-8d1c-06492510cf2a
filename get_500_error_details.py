#!/usr/bin/env python3
"""
获取500错误的详细信息
"""

import requests
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:5000"

def get_500_error_details():
    """获取500错误的详细信息"""
    try:
        # 创建会话并登录
        session = requests.Session()
        
        # 登录
        login_data = {
            'username': 'admin',
            'password': 'admin'
        }
        
        login_response = session.post(f"{BASE_URL}/auth/login", data=login_data)
        logger.info(f"登录状态: {login_response.status_code}")
        
        # 访问失败批次页面
        response = session.get(f"{BASE_URL}/production/failed-lots")
        logger.info(f"页面状态码: {response.status_code}")
        
        if response.status_code == 500:
            logger.error("❌ 500内部服务器错误")
            logger.info("错误响应内容:")
            print("=" * 60)
            print(response.text)
            print("=" * 60)
            
            # 保存错误内容
            with open('failed_lots_500_error.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            logger.info("错误内容已保存到 failed_lots_500_error.html")
            
        else:
            logger.info(f"页面状态正常: {response.status_code}")
            
    except Exception as e:
        logger.error(f"获取错误详情失败: {e}")

if __name__ == "__main__":
    get_500_error_details()
