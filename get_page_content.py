#!/usr/bin/env python3
"""
获取失败批次页面的实际内容
"""

import requests
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:5000"

def get_page_content():
    """获取页面内容"""
    try:
        url = f"{BASE_URL}/production/failed-lots"
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            content = response.text
            logger.info(f"页面内容长度: {len(content)} 字符")
            
            # 保存到文件
            with open('failed_lots_page_content.html', 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info("页面内容已保存到 failed_lots_page_content.html")
            
            # 显示前1000字符
            logger.info("页面内容前1000字符:")
            print("=" * 60)
            print(content[:1000])
            print("=" * 60)
            
            # 显示后1000字符
            logger.info("页面内容后1000字符:")
            print("=" * 60)
            print(content[-1000:])
            print("=" * 60)
            
        else:
            logger.error(f"页面访问失败: {response.status_code}")
            logger.error(f"响应内容: {response.text}")
            
    except Exception as e:
        logger.error(f"获取页面内容失败: {e}")

if __name__ == "__main__":
    get_page_content()
