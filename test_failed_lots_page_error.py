#!/usr/bin/env python3
"""
测试失败批次页面的具体错误
"""

import requests
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:5000"

def test_page_access():
    """测试页面访问"""
    try:
        logger.info("🔍 测试失败批次页面访问...")
        
        url = f"{BASE_URL}/production/failed-lots"
        logger.info(f"📡 请求URL: {url}")
        
        response = requests.get(url, timeout=30)
        logger.info(f"📊 响应状态码: {response.status_code}")
        logger.info(f"📊 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            logger.info("✅ 页面访问成功")
            content = response.text
            logger.info(f"📊 页面内容长度: {len(content)} 字符")
            
            # 检查关键内容
            key_checks = [
                ("失败批次详情", "页面标题"),
                ("loadFailedLots", "JavaScript函数"),
                ("failedLotsTableBody", "表格元素"),
                ("searchInput", "搜索框"),
                ("loadingOverlay", "加载遮罩")
            ]
            
            for check_text, description in key_checks:
                if check_text in content:
                    logger.info(f"✅ 包含{description}: {check_text}")
                else:
                    logger.warning(f"⚠️ 缺少{description}: {check_text}")
            
            # 检查是否有明显的错误
            error_indicators = [
                "500 Internal Server Error",
                "404 Not Found",
                "Traceback",
                "Exception",
                "Error:",
                "NameError",
                "ImportError",
                "TemplateNotFound"
            ]
            
            for error in error_indicators:
                if error in content:
                    logger.error(f"❌ 发现错误指示器: {error}")
                    # 显示错误上下文
                    start = max(0, content.find(error) - 200)
                    end = min(len(content), content.find(error) + 200)
                    context = content[start:end]
                    logger.error(f"错误上下文: {context}")
                    return False
            
            logger.info("✅ 页面内容看起来正常")
            return True
            
        elif response.status_code == 404:
            logger.error("❌ 页面未找到 (404)")
            logger.error(f"响应内容: {response.text[:500]}")
            return False
            
        elif response.status_code == 500:
            logger.error("❌ 服务器内部错误 (500)")
            logger.error(f"响应内容: {response.text[:1000]}")
            return False
            
        elif response.status_code == 302 or response.status_code == 301:
            logger.warning(f"⚠️ 页面重定向 ({response.status_code})")
            logger.info(f"重定向到: {response.headers.get('Location', 'Unknown')}")
            return False
            
        else:
            logger.error(f"❌ 未知状态码: {response.status_code}")
            logger.error(f"响应内容: {response.text[:500]}")
            return False
            
    except requests.exceptions.ConnectionError as e:
        logger.error(f"❌ 连接错误: {e}")
        logger.error("💡 请确保Flask应用正在运行")
        return False
        
    except requests.exceptions.Timeout as e:
        logger.error(f"❌ 请求超时: {e}")
        return False
        
    except Exception as e:
        logger.error(f"❌ 测试页面访问失败: {e}")
        return False

def test_login_status():
    """测试登录状态"""
    try:
        logger.info("🔍 测试登录状态...")
        
        # 先测试登录页面
        login_url = f"{BASE_URL}/auth/login"
        response = requests.get(login_url, timeout=10)
        
        if response.status_code == 200:
            logger.info("✅ 登录页面可访问")
        else:
            logger.warning(f"⚠️ 登录页面状态异常: {response.status_code}")
        
        # 测试主页
        home_url = f"{BASE_URL}/"
        response = requests.get(home_url, timeout=10)
        
        if response.status_code == 200:
            logger.info("✅ 主页可访问")
        elif response.status_code == 302:
            logger.info("🔄 主页重定向到登录页面（正常）")
        else:
            logger.warning(f"⚠️ 主页状态异常: {response.status_code}")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试登录状态失败: {e}")
        return False

def test_api_endpoints():
    """测试相关API端点"""
    try:
        logger.info("🔍 测试相关API端点...")
        
        # 测试失败批次API
        api_url = f"{BASE_URL}/api/v2/production/get-failed-lots-from-logs?current_only=true"
        response = requests.get(api_url, timeout=30)
        
        logger.info(f"📊 API状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info("✅ 失败批次API正常")
            logger.info(f"📊 返回数据: {data.get('success', False)}")
            if data.get('success'):
                failed_lots = data.get('data', {}).get('failed_lots', [])
                logger.info(f"📊 失败批次数量: {len(failed_lots)}")
        elif response.status_code == 401:
            logger.warning("⚠️ API需要登录认证")
        elif response.status_code == 403:
            logger.warning("⚠️ API权限不足")
        else:
            logger.error(f"❌ API状态异常: {response.status_code}")
            logger.error(f"API响应: {response.text[:500]}")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试API端点失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始测试失败批次页面错误...")
    logger.info("=" * 60)
    
    # 1. 测试登录状态
    test_login_status()
    
    print("-" * 40)
    
    # 2. 测试页面访问
    page_ok = test_page_access()
    
    print("-" * 40)
    
    # 3. 测试API端点
    test_api_endpoints()
    
    logger.info("=" * 60)
    
    if page_ok:
        logger.info("🎉 页面访问测试通过")
        logger.info("💡 如果浏览器中仍有问题，请：")
        logger.info("   1. 清除浏览器缓存 (Ctrl+Shift+Delete)")
        logger.info("   2. 强制刷新页面 (Ctrl+F5)")
        logger.info("   3. 检查浏览器控制台错误 (F12)")
    else:
        logger.error("❌ 页面访问测试失败")
        logger.info("💡 请检查：")
        logger.info("   1. Flask应用是否正在运行")
        logger.info("   2. 是否已登录系统")
        logger.info("   3. 路由配置是否正确")
        logger.info("   4. 模板文件是否存在")

if __name__ == "__main__":
    main()
