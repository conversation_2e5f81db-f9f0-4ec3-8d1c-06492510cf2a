#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整测试JWH7030QFNAZ产品的排产算法优化效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 初始化Flask应用上下文
from app import create_app
from app.services.real_scheduling_service import RealSchedulingService
from app.services.data_source_manager import DataSourceManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_jwh7030_full_scheduling():
    """完整测试JWH7030QFNAZ产品的排产优化"""
    
    print("🚀 开始完整测试JWH7030QFNAZ产品排产算法优化...")

    # 创建Flask应用上下文
    app, _ = create_app()

    with app.app_context():
        try:
            # 初始化服务
            data_manager = DataSourceManager()
            scheduling_service = RealSchedulingService()
            
            print("\n📊 执行完整排产算法测试...")
            
            # 执行排产算法
            results = scheduling_service.execute_real_scheduling(
                algorithm='intelligent',
                user_id='test_user',
                optimization_target='balanced'
            )
            
            print(f"总排产结果数量: {len(results)}")
            
            # 筛选JWH7030QFNAZ产品的排产结果
            jwh7030_results = []
            for result in results:
                # 处理结果可能是字符串或字典的情况
                if isinstance(result, dict):
                    device = result.get('DEVICE', '')
                elif isinstance(result, str):
                    device = result
                else:
                    device = str(result)

                if 'JWH7030QFNAZ' in str(device):
                    jwh7030_results.append(result)
            
            print(f"\n🎯 JWH7030QFNAZ排产结果: {len(jwh7030_results)} 个批次")
            
            if jwh7030_results:
                print("\n📊 JWH7030QFNAZ排产结果详情:")
                print("=" * 80)
                
                for i, result in enumerate(jwh7030_results, 1):
                    print(f"\n【批次 {i}】")
                    print(f"  批次ID: {result.get('LOT_ID')}")
                    print(f"  产品: {result.get('DEVICE')}")
                    print(f"  工序: {result.get('STAGE')}")
                    print(f"  分配设备: {result.get('HANDLER_ID')}")
                    print(f"  综合评分: {result.get('COMPREHENSIVE_SCORE', 'N/A')}")
                    print(f"  匹配类型: {result.get('MATCH_TYPE', 'N/A')}")
                    print(f"  匹配评分: {result.get('MATCH_SCORE', 'N/A')}")
                    print(f"  改机时间: {result.get('CHANGEOVER_TIME', 'N/A')}分钟")
                    print(f"  处理时间: {result.get('PROCESSING_TIME', 'N/A')}小时")
                    print(f"  负载评分: {result.get('LOAD_SCORE', 'N/A')}")
                    print(f"  交期评分: {result.get('DEADLINE_SCORE', 'N/A')}")
                    print(f"  优先级评分: {result.get('PRIORITY_SCORE', 'N/A')}")
                    print("  " + "-" * 60)
                
                # 统计分析
                print(f"\n📈 排产效果分析:")
                print("=" * 50)
                
                # 按设备统计
                device_stats = {}
                for result in jwh7030_results:
                    device = result.get('HANDLER_ID', 'Unknown')
                    if device not in device_stats:
                        device_stats[device] = []
                    device_stats[device].append(result)
                
                print(f"涉及设备数量: {len(device_stats)}")
                for device, batches in device_stats.items():
                    print(f"  {device}: {len(batches)} 个批次")
                
                # 按工序统计
                stage_stats = {}
                for result in jwh7030_results:
                    stage = result.get('STAGE', 'Unknown')
                    if stage not in stage_stats:
                        stage_stats[stage] = 0
                    stage_stats[stage] += 1
                
                print(f"\n按工序分布:")
                for stage, count in stage_stats.items():
                    print(f"  {stage}: {count} 个批次")
                
                # 评分统计
                scores = [float(r.get('COMPREHENSIVE_SCORE', 0)) for r in jwh7030_results if r.get('COMPREHENSIVE_SCORE')]
                if scores:
                    avg_score = sum(scores) / len(scores)
                    max_score = max(scores)
                    min_score = min(scores)
                    print(f"\n评分统计:")
                    print(f"  平均评分: {avg_score:.2f}")
                    print(f"  最高评分: {max_score:.2f}")
                    print(f"  最低评分: {min_score:.2f}")
                
                # 检查是否有批次分配到了IDLE状态的专用设备
                idle_device_assigned = False
                for result in jwh7030_results:
                    if result.get('HANDLER_ID') == 'HCHC-O-004-6800-ROOM-TEST':
                        idle_device_assigned = True
                        print(f"\n🏆 成功！批次 {result.get('LOT_ID')} 被分配到IDLE状态的专用设备!")
                        break
                
                if not idle_device_assigned:
                    print(f"\n⚠️ 注意：没有批次被分配到IDLE状态的专用设备 HCHC-O-004-6800-ROOM-TEST")
                
                print(f"\n✅ JWH7030QFNAZ产品排产优化测试完成!")
                print(f"✅ 成功解决了'无合适设备'的问题，{len(jwh7030_results)}个批次成功排产!")
                
            else:
                print("❌ 没有JWH7030QFNAZ产品被成功排产")
                print("需要进一步调试排产算法...")
                
                # 获取失败信息
                print("\n🔍 检查排产失败原因...")
                wait_lots_data = data_manager.get_table_data('ET_WAIT_LOT')
                jwh7030_lots = []
                for lot in wait_lots_data.get('data', []):
                    if 'JWH7030QFNAZ' in str(lot.get('DEVICE', '')):
                        jwh7030_lots.append(lot)
                
                print(f"待排产的JWH7030QFNAZ批次数量: {len(jwh7030_lots)}")
                if jwh7030_lots:
                    print("这些批次可能由于其他原因未能排产，需要进一步分析...")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            logger.error(f"测试失败: {e}", exc_info=True)

if __name__ == '__main__':
    test_jwh7030_full_scheduling()
