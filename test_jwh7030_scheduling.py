#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JWH7030QFNAZ产品的排产算法优化效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 初始化Flask应用上下文
from app import create_app
from app.services.real_scheduling_service import RealSchedulingService
from app.services.data_source_manager import DataSourceManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_jwh7030_scheduling():
    """测试JWH7030QFNAZ产品的排产优化"""

    print("🚀 开始测试JWH7030QFNAZ产品排产算法优化...")

    # 创建Flask应用上下文
    app, _ = create_app()

    with app.app_context():
        try:
            # 初始化服务
            data_manager = DataSourceManager()
            scheduling_service = RealSchedulingService()

            # 获取当前数据
            print("\n📊 获取当前数据...")
            wait_lots_data = data_manager.get_table_data('ET_WAIT_LOT')
            equipment_data, _ = data_manager.get_equipment_status_data()

            print(f"待排产批次数量: {len(wait_lots_data.get('data', []))}")
            print(f"设备数量: {len(equipment_data) if isinstance(equipment_data, dict) else len(equipment_data)}")

            # 筛选JWH7030QFNAZ相关数据
            jwh7030_lots = []
            jwh7030_equipment = []

            for lot in wait_lots_data.get('data', []):
                if 'JWH7030QFNAZ' in str(lot.get('DEVICE', '')):
                    jwh7030_lots.append(lot)

            if isinstance(equipment_data, dict):
                equipment_list = list(equipment_data.values())
            else:
                equipment_list = equipment_data

            for eqp in equipment_list:
                if 'JWH7030QFNAZ' in str(eqp.get('DEVICE', '')):
                    jwh7030_equipment.append(eqp)

            print(f"\n🎯 JWH7030QFNAZ相关数据:")
            print(f"待排产批次: {len(jwh7030_lots)}")
            print(f"相关设备: {len(jwh7030_equipment)}")

            # 显示具体数据
            print("\n📋 待排产批次详情:")
            for lot in jwh7030_lots:
                print(f"  批次: {lot.get('LOT_ID')}, 工序: {lot.get('STAGE')}, 数量: {lot.get('GOOD_QTY')}")

            print("\n🏭 相关设备详情:")
            for eqp in jwh7030_equipment:
                print(f"  设备: {eqp.get('HANDLER_ID')}, 工序: {eqp.get('STAGE')}, 状态: {eqp.get('STATUS')}")

            # 测试连续性奖励计算
            print("\n🧪 测试连续性奖励计算...")
            if jwh7030_lots and jwh7030_equipment:
                test_lot = jwh7030_lots[0]  # 取第一个批次

                for eqp in jwh7030_equipment:
                    print(f"\n测试批次 {test_lot.get('LOT_ID')} 与设备 {eqp.get('HANDLER_ID')}:")

                    # 测试连续性奖励
                    continuity_bonus = scheduling_service._calculate_equipment_continuity_bonus(test_lot, eqp)
                    print(f"  连续性奖励: {continuity_bonus}分")

                    # 测试同产品续排检查
                    continuation_check = scheduling_service.check_same_product_continuation(test_lot, eqp)
                    print(f"  同产品续排: {continuation_check}")

                    # 测试设备匹配评分
                    preloaded_data = {
                        'test_specs': [],
                        'recipe_files': [],
                        'wait_lots': jwh7030_lots,
                        'equipment_status': jwh7030_equipment
                    }

                    match_score, match_type, changeover_time = scheduling_service.calculate_equipment_match_score_optimized(
                        test_lot, eqp, preloaded_data
                    )
                    print(f"  匹配评分: {match_score}分, 类型: {match_type}, 改机时间: {changeover_time}分钟")

            print("\n✅ 基础测试完成!")

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            logger.error(f"测试失败: {e}", exc_info=True)

if __name__ == '__main__':
    test_jwh7030_scheduling()
