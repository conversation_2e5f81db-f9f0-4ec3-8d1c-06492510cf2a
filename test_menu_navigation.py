#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
菜单切换性能测试
测试真实的菜单切换体验
"""

import requests
import time
import statistics

def test_menu_navigation():
    """测试菜单切换性能"""
    print("🧪 菜单切换性能测试")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    session = requests.Session()
    
    # 登录
    print("🔐 登录系统...")
    try:
        login_response = session.post(f"{base_url}/auth/login", data={
            "username": "admin",
            "password": "admin"
        }, timeout=10)
        
        if login_response.status_code == 200:
            print("   ✅ 登录成功")
        else:
            print("   ⚠️ 登录状态:", login_response.status_code)
    except Exception as e:
        print(f"   ❌ 登录失败: {e}")
        return
    
    # 测试菜单页面
    menu_pages = [
        ("/", "主页"),
        ("/production/semi-auto", "生产调度"),
        ("/orders/semi-auto", "订单管理"),
        ("/api/v3/universal/et_wait_lot", "数据管理"),
        ("/system/logs", "系统日志"),
        ("/users", "用户管理")
    ]
    
    print("\n🔄 测试菜单切换速度...")
    
    all_times = []
    
    for url, name in menu_pages:
        print(f"\n📄 测试: {name} ({url})")
        
        # 测试3次取平均值
        times = []
        for i in range(3):
            start_time = time.time()
            try:
                response = session.get(f"{base_url}{url}", timeout=10)
                end_time = time.time()
                
                if response.status_code == 200:
                    load_time = (end_time - start_time) * 1000
                    times.append(load_time)
                    print(f"   第{i+1}次: {load_time:.1f}ms ✅")
                else:
                    print(f"   第{i+1}次: 失败 ({response.status_code}) ❌")
                    
            except Exception as e:
                print(f"   第{i+1}次: 错误 ({e}) ❌")
            
            time.sleep(0.2)  # 短暂间隔
        
        if times:
            avg_time = statistics.mean(times)
            min_time = min(times)
            max_time = max(times)
            all_times.extend(times)
            
            print(f"   📊 平均: {avg_time:.1f}ms, 最快: {min_time:.1f}ms, 最慢: {max_time:.1f}ms")
            
            # 性能评级
            if avg_time < 50:
                grade = "🚀 极快"
            elif avg_time < 200:
                grade = "⚡ 很快"
            elif avg_time < 500:
                grade = "👍 快"
            elif avg_time < 1000:
                grade = "👌 正常"
            else:
                grade = "🐌 慢"
            
            print(f"   🏆 评级: {grade}")
        else:
            print(f"   ❌ 所有请求都失败了")
    
    # 总体统计
    if all_times:
        overall_avg = statistics.mean(all_times)
        overall_min = min(all_times)
        overall_max = max(all_times)
        
        print("\n" + "=" * 50)
        print("📊 菜单切换性能总结")
        print("=" * 50)
        print(f"平均切换时间: {overall_avg:.1f}ms")
        print(f"最快切换: {overall_min:.1f}ms")
        print(f"最慢切换: {overall_max:.1f}ms")
        print(f"测试页面数: {len(menu_pages)}")
        print(f"总测试次数: {len(all_times)}")
        
        # 总体评级
        if overall_avg < 50:
            overall_grade = "🚀 极快 - 接近瞬间响应"
            user_experience = "😍 用户体验极佳"
        elif overall_avg < 200:
            overall_grade = "⚡ 很快 - 快速响应"
            user_experience = "😊 用户体验很好"
        elif overall_avg < 500:
            overall_grade = "👍 快 - 良好响应"
            user_experience = "🙂 用户体验良好"
        elif overall_avg < 1000:
            overall_grade = "👌 正常 - 可接受响应"
            user_experience = "😐 用户体验一般"
        else:
            overall_grade = "🐌 慢 - 需要优化"
            user_experience = "😤 用户体验差"
        
        print(f"\n🏆 总体评级: {overall_grade}")
        print(f"👤 {user_experience}")
        
        # 与优化前对比
        print(f"\n📈 性能提升对比:")
        print(f"优化前: ~2000ms (Flask开发服务器)")
        print(f"优化后: {overall_avg:.1f}ms (Waitress生产服务器)")
        
        if overall_avg < 2000:
            improvement = ((2000 - overall_avg) / 2000) * 100
            print(f"🎉 性能提升: {improvement:.1f}%")
        
        # 用户体验分析
        print(f"\n💡 用户体验分析:")
        if overall_avg < 100:
            print("   ✅ 菜单切换感觉瞬间完成")
            print("   ✅ 用户不会感到等待")
            print("   ✅ 接近现代SPA应用体验")
        elif overall_avg < 300:
            print("   ✅ 菜单切换很快")
            print("   ✅ 用户感觉流畅")
            print("   ⚠️ 可以进一步优化到瞬间响应")
        else:
            print("   ⚠️ 仍有优化空间")
            print("   💡 建议启用前端预加载")
    
    else:
        print("\n❌ 没有成功的测试结果")

def main():
    """主函数"""
    # 检查服务器
    try:
        response = requests.get("http://localhost:5000", timeout=5)
        print("✅ 服务器正在运行")
    except:
        print("❌ 服务器未运行，请先启动生产服务器: python run_production.py")
        return
    
    test_menu_navigation()
    
    print("\n🎯 下一步建议:")
    print("1. 在浏览器中实际测试菜单切换体验")
    print("2. 观察浏览器开发者工具的Network面板")
    print("3. 测试前端预加载功能是否工作")
    print("4. 如果体验良好，可以部署到生产环境")

if __name__ == "__main__":
    main()
