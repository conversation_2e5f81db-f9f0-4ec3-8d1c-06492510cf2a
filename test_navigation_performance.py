#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
菜单切换性能实际测试脚本
验证优化效果是否真实有效
"""

import requests
import time
import statistics
from urllib.parse import urljoin
import json

class NavigationPerformanceTester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        
    def login(self, username="admin", password="admin"):
        """登录系统"""
        print("🔐 正在登录系统...")
        
        # 获取登录页面
        login_url = urljoin(self.base_url, "/auth/login")
        response = self.session.get(login_url)
        
        if response.status_code != 200:
            print(f"❌ 无法访问登录页面: {response.status_code}")
            return False
        
        # 提交登录表单
        login_data = {
            "username": username,
            "password": password
        }
        
        response = self.session.post(login_url, data=login_data, allow_redirects=True)
        
        if "dashboard" in response.url or response.status_code == 200:
            print("✅ 登录成功")
            return True
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False
    
    def test_page_load_time(self, path, test_name):
        """测试单个页面加载时间"""
        url = urljoin(self.base_url, path)
        
        # 预热请求（不计入统计）
        try:
            self.session.get(url, timeout=10)
        except:
            pass
        
        # 实际测试
        times = []
        for i in range(5):  # 测试5次取平均值
            start_time = time.time()
            try:
                response = self.session.get(url, timeout=10)
                end_time = time.time()
                
                if response.status_code == 200:
                    load_time = (end_time - start_time) * 1000  # 转换为毫秒
                    times.append(load_time)
                    print(f"   第{i+1}次: {load_time:.1f}ms")
                else:
                    print(f"   第{i+1}次: 失败 ({response.status_code})")
                    
            except Exception as e:
                print(f"   第{i+1}次: 超时或错误 ({e})")
            
            time.sleep(0.5)  # 间隔0.5秒
        
        if times:
            avg_time = statistics.mean(times)
            min_time = min(times)
            max_time = max(times)
            
            result = {
                'test_name': test_name,
                'path': path,
                'avg_time': avg_time,
                'min_time': min_time,
                'max_time': max_time,
                'success_rate': len(times) / 5 * 100
            }
            
            self.test_results.append(result)
            
            print(f"📊 {test_name}:")
            print(f"   平均时间: {avg_time:.1f}ms")
            print(f"   最快时间: {min_time:.1f}ms")
            print(f"   最慢时间: {max_time:.1f}ms")
            print(f"   成功率: {result['success_rate']:.0f}%")
            
            return result
        else:
            print(f"❌ {test_name}: 所有请求都失败了")
            return None
    
    def test_static_resource_caching(self):
        """测试静态资源缓存效果"""
        print("\n🔍 测试静态资源缓存...")
        
        static_resources = [
            "/static/vendor/bootstrap/bootstrap.min.css",
            "/static/vendor/bootstrap/bootstrap.bundle.min.js",
            "/static/js/unified_api_client.js",
            "/static/js/quick-navigation.js"
        ]
        
        for resource in static_resources:
            url = urljoin(self.base_url, resource)
            
            # 第一次请求
            start_time = time.time()
            response1 = self.session.get(url)
            first_time = (time.time() - start_time) * 1000
            
            # 第二次请求（应该从缓存获取）
            start_time = time.time()
            response2 = self.session.get(url)
            second_time = (time.time() - start_time) * 1000
            
            print(f"   {resource}:")
            print(f"     首次请求: {first_time:.1f}ms")
            print(f"     缓存请求: {second_time:.1f}ms")
            print(f"     缓存头部: {response2.headers.get('Cache-Control', 'None')}")
            
            if second_time < first_time * 0.5:
                print(f"     ✅ 缓存生效 (提升{((first_time - second_time) / first_time * 100):.0f}%)")
            else:
                print(f"     ⚠️ 缓存可能未生效")
    
    def test_compression(self):
        """测试压缩效果"""
        print("\n🗜️ 测试压缩效果...")
        
        test_urls = [
            "/",
            "/static/js/unified_api_client.js",
            "/static/vendor/bootstrap/bootstrap.min.css"
        ]
        
        for url_path in test_urls:
            url = urljoin(self.base_url, url_path)
            
            # 请求压缩版本
            headers = {'Accept-Encoding': 'gzip, deflate'}
            response = self.session.get(url, headers=headers)
            
            content_encoding = response.headers.get('Content-Encoding', 'None')
            content_length = len(response.content)
            
            print(f"   {url_path}:")
            print(f"     压缩方式: {content_encoding}")
            print(f"     内容大小: {content_length / 1024:.1f}KB")
            
            if content_encoding in ['gzip', 'deflate']:
                print(f"     ✅ 压缩已启用")
            else:
                print(f"     ⚠️ 压缩未启用")
    
    def test_quick_navigation_api(self):
        """测试快速导航API"""
        print("\n🚀 测试快速导航功能...")
        
        # 检查快速导航脚本是否可访问
        script_url = urljoin(self.base_url, "/static/js/quick-navigation.js")
        response = self.session.get(script_url)
        
        if response.status_code == 200:
            print("   ✅ 快速导航脚本可访问")
            
            # 检查脚本内容
            content = response.text
            if "QuickNavigation" in content:
                print("   ✅ 快速导航类定义存在")
            if "preloadPage" in content:
                print("   ✅ 预加载功能存在")
            if "setupHoverPreload" in content:
                print("   ✅ 悬停预加载功能存在")
        else:
            print(f"   ❌ 快速导航脚本无法访问: {response.status_code}")
    
    def run_comprehensive_test(self):
        """运行综合性能测试"""
        print("🚀 开始菜单切换性能测试...")
        print("=" * 60)
        
        # 登录
        if not self.login():
            print("❌ 登录失败，无法继续测试")
            return
        
        # 测试主要页面加载时间
        test_pages = [
            ("/", "主页"),
            ("/production/semi-auto", "生产调度"),
            ("/orders/semi-auto", "订单管理"),
            ("/api/v2/system/navigation-test", "导航测试页面"),
            ("/api/v2/system/performance-test", "性能测试页面")
        ]
        
        print("\n📊 测试页面加载时间...")
        for path, name in test_pages:
            self.test_page_load_time(path, name)
            print()
        
        # 测试静态资源缓存
        self.test_static_resource_caching()
        
        # 测试压缩
        self.test_compression()
        
        # 测试快速导航功能
        self.test_quick_navigation_api()
        
        # 生成测试报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📋 性能测试报告")
        print("=" * 60)
        
        if not self.test_results:
            print("❌ 没有有效的测试结果")
            return
        
        # 计算总体统计
        all_times = [result['avg_time'] for result in self.test_results]
        overall_avg = statistics.mean(all_times)
        overall_min = min(all_times)
        overall_max = max(all_times)
        
        print(f"📊 总体性能:")
        print(f"   平均加载时间: {overall_avg:.1f}ms")
        print(f"   最快页面: {overall_min:.1f}ms")
        print(f"   最慢页面: {overall_max:.1f}ms")
        
        # 性能评级
        if overall_avg < 500:
            grade = "🟢 优秀"
        elif overall_avg < 1000:
            grade = "🟡 良好"
        elif overall_avg < 2000:
            grade = "🟠 一般"
        else:
            grade = "🔴 需要优化"
        
        print(f"   性能评级: {grade}")
        
        # 详细结果
        print(f"\n📋 详细结果:")
        for result in self.test_results:
            print(f"   {result['test_name']}: {result['avg_time']:.1f}ms (成功率: {result['success_rate']:.0f}%)")
        
        # 优化建议
        print(f"\n💡 优化建议:")
        if overall_avg > 1000:
            print("   - 页面加载时间较长，建议检查网络连接和服务器性能")
        if overall_avg > 2000:
            print("   - 严重性能问题，快速导航优化可能未生效")
        else:
            print("   - 性能表现良好，快速导航优化可能已生效")
        
        # 保存结果到文件
        report_data = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'overall_avg': overall_avg,
            'overall_min': overall_min,
            'overall_max': overall_max,
            'grade': grade,
            'results': self.test_results
        }
        
        with open('navigation_performance_report.json', 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 详细报告已保存到: navigation_performance_report.json")

def main():
    """主函数"""
    print("🧪 菜单切换性能实际测试")
    print("测试目标: 验证快速导航优化是否真实有效")
    print()
    
    # 检查服务器是否运行
    try:
        response = requests.get("http://localhost:5000", timeout=5)
        print("✅ 服务器正在运行")
    except:
        print("❌ 服务器未运行，请先启动应用: python run.py")
        return
    
    # 运行测试
    tester = NavigationPerformanceTester()
    tester.run_comprehensive_test()

if __name__ == "__main__":
    main()
