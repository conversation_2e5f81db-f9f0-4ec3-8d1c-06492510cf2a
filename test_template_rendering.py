#!/usr/bin/env python3
"""
测试模板渲染问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flask import Flask, render_template
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_template_rendering():
    """测试失败批次模板渲染"""
    try:
        # 创建最小Flask应用
        app = Flask(__name__, template_folder='app/templates')
        app.config['SECRET_KEY'] = 'test-key'
        
        with app.app_context():
            logger.info("🔍 测试失败批次模板渲染...")
            
            try:
                # 尝试渲染模板
                html = render_template('production/failed_lots.html')
                logger.info(f"✅ 模板渲染成功，长度: {len(html)} 字符")
                
                # 检查关键内容
                key_checks = [
                    ("失败批次详情", "页面标题"),
                    ("loadFailedLots", "JavaScript函数"),
                    ("failedLotsTableBody", "表格元素"),
                    ("searchInput", "搜索框"),
                    ("loadingOverlay", "加载遮罩")
                ]
                
                missing_elements = []
                for check_text, description in key_checks:
                    if check_text not in html:
                        missing_elements.append(f"{description}({check_text})")
                
                if missing_elements:
                    logger.warning(f"⚠️ 模板缺少关键元素: {missing_elements}")
                else:
                    logger.info("✅ 模板包含所有关键元素")
                
                # 保存渲染结果
                with open('template_render_test.html', 'w', encoding='utf-8') as f:
                    f.write(html)
                logger.info("📄 模板渲染结果已保存到 template_render_test.html")
                
                return True
                
            except Exception as template_error:
                logger.error(f"❌ 模板渲染失败: {template_error}")
                logger.error(f"错误类型: {type(template_error).__name__}")
                logger.error(f"错误详情: {str(template_error)}")
                
                # 检查模板文件是否存在
                template_path = "app/templates/production/failed_lots.html"
                if os.path.exists(template_path):
                    logger.info("✅ 模板文件存在")
                    
                    # 检查文件大小
                    file_size = os.path.getsize(template_path)
                    logger.info(f"📊 模板文件大小: {file_size} 字节")
                    
                    # 检查文件编码
                    try:
                        with open(template_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        logger.info(f"📊 模板内容长度: {len(content)} 字符")
                        
                        # 检查是否有明显的语法错误
                        if "{{" in content and "}}" in content:
                            logger.info("✅ 模板包含Jinja2语法")
                        else:
                            logger.warning("⚠️ 模板可能缺少Jinja2语法")
                            
                    except UnicodeDecodeError as e:
                        logger.error(f"❌ 模板文件编码错误: {e}")
                        
                else:
                    logger.error("❌ 模板文件不存在")
                
                return False
                
    except Exception as e:
        logger.error(f"❌ 测试模板渲染失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始测试模板渲染...")
    
    success = test_template_rendering()
    
    if success:
        logger.info("🎉 模板渲染测试成功！")
        logger.info("💡 模板本身没有问题，500错误可能来自其他原因：")
        logger.info("   1. 数据库连接问题")
        logger.info("   2. 权限检查问题")
        logger.info("   3. 依赖模块导入问题")
        logger.info("   4. Flask应用配置问题")
    else:
        logger.error("❌ 模板渲染测试失败！")
        logger.info("💡 请检查：")
        logger.info("   1. 模板文件语法")
        logger.info("   2. 模板文件编码")
        logger.info("   3. 模板文件完整性")

if __name__ == "__main__":
    main()
