#!/usr/bin/env python3
"""
带登录的失败批次页面测试
"""

import requests
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:5000"

def login_and_test():
    """登录并测试失败批次页面"""
    try:
        # 创建会话
        session = requests.Session()
        
        # 1. 先获取登录页面
        logger.info("🔍 获取登录页面...")
        login_page = session.get(f"{BASE_URL}/auth/login")
        if login_page.status_code != 200:
            logger.error(f"❌ 无法访问登录页面: {login_page.status_code}")
            return False
        
        # 2. 执行登录
        logger.info("🔐 执行登录...")
        login_data = {
            'username': 'admin',
            'password': 'admin'
        }
        
        login_response = session.post(f"{BASE_URL}/auth/login", data=login_data)
        logger.info(f"📊 登录响应状态码: {login_response.status_code}")
        
        # 检查是否登录成功（通常会重定向到主页）
        if login_response.status_code == 302 or login_response.status_code == 200:
            logger.info("✅ 登录成功")
        else:
            logger.error(f"❌ 登录失败: {login_response.status_code}")
            logger.error(f"响应内容: {login_response.text[:500]}")
            return False
        
        # 3. 测试失败批次页面
        logger.info("🔍 测试失败批次页面...")
        failed_lots_response = session.get(f"{BASE_URL}/production/failed-lots")
        logger.info(f"📊 失败批次页面状态码: {failed_lots_response.status_code}")
        
        if failed_lots_response.status_code == 200:
            content = failed_lots_response.text
            logger.info(f"📊 页面内容长度: {len(content)} 字符")
            
            # 检查关键内容
            key_checks = [
                ("失败批次详情", "页面标题"),
                ("loadFailedLots", "JavaScript函数"),
                ("failedLotsTableBody", "表格元素"),
                ("searchInput", "搜索框"),
                ("loadingOverlay", "加载遮罩")
            ]
            
            all_found = True
            for check_text, description in key_checks:
                if check_text in content:
                    logger.info(f"✅ 包含{description}: {check_text}")
                else:
                    logger.warning(f"⚠️ 缺少{description}: {check_text}")
                    all_found = False
            
            if all_found:
                logger.info("🎉 失败批次页面内容完整！")
            else:
                logger.warning("⚠️ 失败批次页面内容不完整")
                
                # 保存页面内容用于调试
                with open('failed_lots_logged_in_content.html', 'w', encoding='utf-8') as f:
                    f.write(content)
                logger.info("📄 页面内容已保存到 failed_lots_logged_in_content.html")
            
            # 4. 测试失败批次API
            logger.info("🔍 测试失败批次API...")
            api_response = session.get(f"{BASE_URL}/api/v2/production/get-failed-lots-from-logs?current_only=true")
            logger.info(f"📊 API状态码: {api_response.status_code}")
            
            if api_response.status_code == 200:
                api_data = api_response.json()
                if api_data.get('success'):
                    failed_lots = api_data.get('data', {}).get('failed_lots', [])
                    logger.info(f"✅ API正常，返回 {len(failed_lots)} 条失败批次")
                else:
                    logger.error(f"❌ API返回失败: {api_data.get('message', '未知错误')}")
            else:
                logger.error(f"❌ API调用失败: {api_response.status_code}")
            
            return True
            
        else:
            logger.error(f"❌ 失败批次页面访问失败: {failed_lots_response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 登录测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始带登录的失败批次页面测试...")
    logger.info("=" * 60)
    
    success = login_and_test()
    
    logger.info("=" * 60)
    
    if success:
        logger.info("🎉 测试成功！")
        logger.info("💡 解决方案：")
        logger.info("   1. 确保已登录系统（用户名: admin, 密码: admin）")
        logger.info("   2. 访问: http://localhost:5000/production/failed-lots")
        logger.info("   3. 如果仍有问题，请强制刷新页面 (Ctrl+F5)")
    else:
        logger.error("❌ 测试失败！")
        logger.info("💡 请检查：")
        logger.info("   1. Flask应用是否正在运行")
        logger.info("   2. 登录凭据是否正确")
        logger.info("   3. 数据库连接是否正常")

if __name__ == "__main__":
    main()
