#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WSGI入口文件
用于生产环境部署
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置环境变量
os.environ.setdefault('FLASK_ENV', 'production')

# 导入应用
from app import create_app

# 创建应用实例
app, socketio = create_app()

# WSGI应用对象
application = app

if __name__ == "__main__":
    # 如果直接运行此文件，使用SocketIO运行
    socketio.run(app, host='127.0.0.1', port=5000, debug=False)
